using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface ISubscriptionFeePaymentRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(SubscriptionFeePaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
