using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Abstractions.UnitOfWork;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services;

/// <summary>
/// Service for managing sequence numbers for custom payment ordering
/// </summary>
public class SequenceNumberService(
    IPaymentRequestRepository paymentRequestRepository,
    ICustomOrderingConfigService customOrderingConfigService,
    IUnitOfWork unitOfWork,
    IDateProvider dateProvider,
    ILogger<SequenceNumberService> logger) : ISequenceNumberService
{
    public async Task<int> AssignSequenceNumber(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        try
        {
            // Check if custom ordering is supported for this payment
            var isSupported = await customOrderingConfigService.IsCustomOrderingSupported(
                paymentRequest.RequestType, paymentRequest.PaymentMethod, ct);

            if (!isSupported)
            {
                logger.LogDebug("Custom ordering not supported for payment request {PaymentRequestId}", paymentRequest.Id);
                return 0;
            }

            var config = await customOrderingConfigService.GetCustomOrderingConfig(ct);
            if (!config.AutoAssignSequenceNumbers)
            {
                logger.LogDebug("Auto-assignment of sequence numbers is disabled");
                return 0;
            }

            // Get next sequence number for this queue
            var sequenceNumber = await GetNextSequenceNumber(paymentRequest.PaymentMethod, paymentRequest.RequestType, ct);
            
            paymentRequest.SequenceNumber = sequenceNumber;
            paymentRequest.UpdatedAt = dateProvider.CurrentDateTime;

            logger.LogInformation("Assigned sequence number {SequenceNumber} to payment request {PaymentRequestId}", 
                sequenceNumber, paymentRequest.Id);

            return sequenceNumber;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error assigning sequence number to payment request {PaymentRequestId}", paymentRequest.Id);
            return 0;
        }
    }

    public async Task<int> GetNextSequenceNumber(PaymentMethod paymentMethod, PaymentRequestType paymentRequestType, CancellationToken ct)
    {
        try
        {
            // Get the maximum sequence number for pending payments of the same type and method
            var maxSequenceNumber = await paymentRequestRepository.GetMaxSequenceNumber(
                paymentMethod, paymentRequestType, PaymentRequestStatus.Approved, ct);

            var nextSequenceNumber = maxSequenceNumber + 1;
            
            logger.LogDebug("Next sequence number for {PaymentMethod}/{PaymentRequestType}: {SequenceNumber}", 
                paymentMethod, paymentRequestType, nextSequenceNumber);

            return nextSequenceNumber;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting next sequence number for {PaymentMethod}/{PaymentRequestType}", 
                paymentMethod, paymentRequestType);
            return 1; // Default to 1 if there's an error
        }
    }

    public async Task<int> UpdateSequenceNumbers(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct)
    {
        if (!paymentRequestIds.Any())
        {
            return 0;
        }

        try
        {
            // Validate that all payment requests can be reordered together
            var canReorder = await ValidateReorderingRestrictions(paymentRequestIds, ct);
            if (!canReorder)
            {
                throw new InvalidOperationException("Payment requests cannot be reordered together - they belong to different queues");
            }

            var config = await customOrderingConfigService.GetCustomOrderingConfig(ct);
            if (paymentRequestIds.Count > config.MaxReorderBatchSize)
            {
                throw new InvalidOperationException($"Cannot reorder more than {config.MaxReorderBatchSize} payments at once");
            }

            using var transaction = await unitOfWork.BeginTransactionAsync(ct);
            try
            {
                // Get all payment requests
                var paymentRequests = await paymentRequestRepository.GetByIds(paymentRequestIds, ct);
                
                // Check if any new payments were approved meanwhile and add them to the end
                var firstPaymentRequest = paymentRequests.First();
                var newlyApprovedPayments = await GetNewlyApprovedPayments(
                    firstPaymentRequest.PaymentMethod, 
                    firstPaymentRequest.RequestType, 
                    paymentRequestIds, 
                    ct);

                // Combine the reordered payments with newly approved ones
                var allPaymentIds = paymentRequestIds.Concat(newlyApprovedPayments.Select(p => p.Id)).ToList();
                
                // Assign new sequence numbers starting from 1
                var updatedCount = 0;
                for (int i = 0; i < allPaymentIds.Count; i++)
                {
                    var paymentId = allPaymentIds[i];
                    var payment = paymentRequests.FirstOrDefault(p => p.Id == paymentId) ?? 
                                 newlyApprovedPayments.FirstOrDefault(p => p.Id == paymentId);
                    
                    if (payment != null)
                    {
                        payment.SequenceNumber = i + 1;
                        payment.UpdatedAt = dateProvider.CurrentDateTime;
                        payment.UpdatedBy = updatedBy;
                        
                        await paymentRequestRepository.Update(payment, ct);
                        updatedCount++;
                    }
                }

                await transaction.CommitAsync(ct);
                
                logger.LogInformation("Updated sequence numbers for {Count} payment requests by {UpdatedBy}", 
                    updatedCount, updatedBy);

                return updatedCount;
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating sequence numbers for payment requests");
            throw;
        }
    }

    public async Task<bool> ValidateReorderingRestrictions(List<Guid> paymentRequestIds, CancellationToken ct)
    {
        if (!paymentRequestIds.Any())
        {
            return true;
        }

        try
        {
            var paymentRequests = await paymentRequestRepository.GetByIds(paymentRequestIds, ct);
            
            if (paymentRequests.Count != paymentRequestIds.Count)
            {
                logger.LogWarning("Some payment requests not found during validation");
                return false;
            }

            // Check that all payments have the same method and type (same queue)
            var firstPayment = paymentRequests.First();
            var allSameQueue = paymentRequests.All(p => 
                p.PaymentMethod == firstPayment.PaymentMethod && 
                p.RequestType == firstPayment.RequestType &&
                p.Status == PaymentRequestStatus.Approved);

            if (!allSameQueue)
            {
                logger.LogWarning("Payment requests belong to different queues or have different statuses");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating reordering restrictions");
            return false;
        }
    }

    public async Task<int> ResetAllSequenceNumbers(string updatedBy, CancellationToken ct)
    {
        try
        {
            using var transaction = await unitOfWork.BeginTransactionAsync(ct);
            try
            {
                var updatedCount = await paymentRequestRepository.ResetAllSequenceNumbers(updatedBy, dateProvider.CurrentDateTime, ct);
                await transaction.CommitAsync(ct);
                
                logger.LogInformation("Reset sequence numbers for {Count} payment requests by {UpdatedBy}", 
                    updatedCount, updatedBy);

                return updatedCount;
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error resetting sequence numbers");
            throw;
        }
    }

    private async Task<List<PaymentRequestEntity>> GetNewlyApprovedPayments(
        PaymentMethod paymentMethod, 
        PaymentRequestType paymentRequestType, 
        List<Guid> existingPaymentIds, 
        CancellationToken ct)
    {
        try
        {
            return await paymentRequestRepository.GetNewlyApprovedPayments(
                paymentMethod, paymentRequestType, existingPaymentIds, ct);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting newly approved payments");
            return new List<PaymentRequestEntity>();
        }
    }
}
