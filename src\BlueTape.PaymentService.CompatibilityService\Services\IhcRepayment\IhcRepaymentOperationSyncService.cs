using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.IhcRepayment;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;

namespace BlueTape.PaymentService.CompatibilityService.Services.IhcRepayment;

public class IhcRepaymentOperationSyncService(
    IOperationsRepository operationsRepository,
    IIhcRepaymentTransactionSyncService transactionSyncService,
    IOperationPaymentSyncHelper paymentSyncHelper,
    IIhcRepaymentCompatibilityMapper compatibilityMapper) : IIhcRepaymentOperationSyncService
{
    public async Task PerformOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var payableIds = paymentRequest.PaymentRequestPayables.Select(x => x.PayableId).ToList();
        var operations = await operationsRepository.GetByOwnerIds(
            payableIds, [LegacyPaymentFlowConstants.InvoicePaymentOperationType], cancellationToken, true);

        foreach (var operation in operations)
        {
            var status = paymentRequest.GetNoSuccessOperationStatus();

            if (operation.Status == OperationStatus.PROCESSING.ToString() && status == OperationStatus.PLACED)
                status = OperationStatus.PROCESSING;
            await operationsRepository.UpdateByOwnerId(operation.OwnerId, operation.Type, new UpdateOperationEntity()
            {
                Status = status.ToString(),
                FirstTransactionDate = paymentRequest.GetFirstTransactionDate(),
                PayeeId = paymentRequest.PayeeId,
                PayerId = paymentRequest.PayerId,
                PullResult = paymentRequest.Status == PaymentRequestStatus.Settled ? DateTime.Now : null,
            }, cancellationToken);

            await transactionSyncService.PerformTransactions(paymentRequest, operation, cancellationToken);
        }

        await paymentSyncHelper.SyncOperationPaymentData(payableIds, cancellationToken);
    }

    public async Task SyncOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var existingOperations = await operationsRepository.GetByOwnerIds(
            paymentRequest.PaymentRequestPayables.Select(x => x.PayableId),
            [LegacyPaymentFlowConstants.InvoicePaymentOperationType], cancellationToken, true);

        if (existingOperations == null || !existingOperations.Any())
        {
            await PerformOperation(paymentRequest, cancellationToken);
            return;
        }

        foreach (var operation in existingOperations)
        {
            var updateOperationEntity = GetUpdateOperationEntity(paymentRequest, operation);
            await operationsRepository.UpdateByOwnerId(operation.OwnerId, operation.Type, updateOperationEntity, cancellationToken);

            await transactionSyncService.SyncTransactions(paymentRequest, operation, cancellationToken);

            await paymentSyncHelper.SyncOperationPaymentData(operation?.BlueTapeId, cancellationToken);
        }
    }

    private UpdateOperationEntity GetUpdateOperationEntity(PaymentRequestEntity paymentRequest, OperationEntity operation)
    {
        var pullResult = paymentRequest.Transactions.FirstOrDefault(x => x.Status == TransactionStatus.Cleared)?.UpdatedAt;

        var status = paymentRequest.GetNoSuccessOperationStatus();
        var isAnyProcessingTransactions =
            operation.Transactions?.Where(x => x.PaymentRequestId != paymentRequest.Id.ToString())
                .Any(x => x.Status == LegacyTransactionStatus.PROCESSING.ToString());

        if (operation.Status == OperationStatus.PROCESSING.ToString() && status == OperationStatus.PLACED)
            status = OperationStatus.PROCESSING;

        if (status == OperationStatus.FAIL && isAnyProcessingTransactions.HasValue && isAnyProcessingTransactions.Value)
            status = OperationStatus.PROCESSING;

        var updateOperationEntity = new UpdateOperationEntity()
        {
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            Status = status.ToString(),
            PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
            PayerId = paymentRequest.PayerId,
            PayeeId = paymentRequest.PayeeId,
            PullResult = pullResult,
        };

        return updateOperationEntity;
    }
}
