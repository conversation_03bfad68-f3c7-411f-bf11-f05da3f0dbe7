﻿using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Reporting.Application.Abstractions.LMS;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

public class FactoringDisbursementCreationService(
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IInvoiceHttpClient invoiceHttpClient,
    IPaymentFlowTemplatesEngine templatesEngine,
    ILogger<FactoringDisbursementCreationService> logger,
    IOperationSyncMessageSender operationSyncMessageSender,
    IPaymentRequestPayableService paymentRequestPayableService,
    IPaymentRequestValidator validator,
    ILoanManagementService loanManagementService,
    ILoanReportingService loanReportingService,
    IDrawApprovalRepository drawApprovalRepository) : BasePaymentRequestCreationService(
    mapper, unitOfWork, templatesEngine, invoiceHttpClient, logger, operationSyncMessageSender, paymentRequestPayableService, loanManagementService), IFactoringDisbursementRequestCreationService
{
    public async Task<PaymentRequestModel> Add(FactoringDisbursementRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct)
    {
        using (logger.BeginScope(new Dictionary<string, object>
               {
                   { "FlowTemplateCode", paymentRequestMessage.FlowTemplateCode },
                   { "SellerCompanyId", paymentRequestMessage.PaymentRequestDetails.SellerDetails.CompanyId },
                   { "PaymentMethod", paymentRequestMessage.PaymentRequestDetails.PaymentMethod },
               }))
        {
            logger.LogInformation("Create payment request started. Payload: {@paymentRequestMessage}", paymentRequestMessage);

            var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(paymentRequestMessage);
            // One day delay for this template, see documentation for it
            createPaymentRequest.MerchantAchDelayInBusinessDays = templatesEngine.GetPaymentRequestDelay(createPaymentRequest.FlowTemplateCode);

            var drawApproval = await drawApprovalRepository.GetByInvoicesIds(createPaymentRequest.PaymentRequestPayables.Select(x => x.Id).ToArray(), ct);
            if (drawApproval != null)
            {
                var loan = (await loanReportingService.GetLoansByDrawApprovalIdsAsync([drawApproval.Id], ct)).FirstOrDefault();

                if (loan != null)
                    createPaymentRequest.DrawId = loan.Id;
            }

            return await Add(createPaymentRequest, createdBy, ct);
        }
    }

    protected override Task SendNotification(CreatePaymentRequestModel createPaymentRequest, PaymentRequestEntity paymentRequestEntity,
        IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct)
    {
        // ToDo: ledger, notifications
        return Task.CompletedTask;
    }

    protected override Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices,
        List<PaymentRequestPayableModel> processedPayables, LoanDto? loan, CancellationToken ct)
    {
        return validator.ValidateSupplierDisbursementRequest(createPaymentRequest, existingInvoices, processedPayables, ct);
    }
}
