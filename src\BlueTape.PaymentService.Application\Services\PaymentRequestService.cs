﻿using AutoMapper;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Models.Base;
using BlueTape.PaymentService.Application.Services.Base;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Mapper;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models.Raw;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Services;

public class PaymentRequestService(
    IPaymentRequestRepository repository,
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IPaymentFlowTemplatesEngine templatesEngine,
    ICompanyHttpClient companyHttpClient,
    ICompanyService companyService,
    ILogger<PaymentRequestService> logger,
    IOptions<RawFlowTemplateOptions> payNowFlowTemplateOptions,
    ISlackNotificationService notificationService,
    IOperationSyncMessageSender operationSyncMessageSender,
    IDateProvider dateProvider,
    ISequenceNumberService sequenceNumberService
) :
    GenericService<PaymentRequestModel, PaymentRequestEntity>(repository, mapper), IPaymentRequestService
{
    private readonly List<FlowTemplate> _flowTemplates =
        payNowFlowTemplateOptions.Value.Templates.Select(rawTemplate => rawTemplate.Map()).ToList();

    public override async Task<PaymentRequestModel> GetById(Guid id, CancellationToken ct)
    {
        var paymentRequest = await repository.GetById(id, ct);
        return Mapper.Map<PaymentRequestModel>(paymentRequest);
    }

    public async Task StartRollBackById(Guid paymentRequestId, string createdBy, CancellationToken ctx)
    {
        var commands = (await unitOfWork
                .Get<PaymentRequestCommandEntity>(ctx, x => x.PaymentRequestId == paymentRequestId, null,
                    $"{nameof(PaymentRequestCommandEntity.Transaction)},{nameof(PaymentRequestCommandEntity.PaymentRequest)}"))
            .ToList();

        await StartRollBack(commands, createdBy, ctx);
    }

    public async Task StartRollBack(List<PaymentRequestCommandEntity> paymentRequestCommands, string createdBy,
        CancellationToken ctx)
    {
        var paymentRequest = paymentRequestCommands.FirstOrDefault()?.PaymentRequest;

        if (paymentRequest?.FlowTemplateCode.Equals(DomainConstants.DrawRepayment) ?? true)
        {
            return;
        }

        if (paymentRequestCommands.Exists(x => x.Status is (CommandStatus.Pending or CommandStatus.Executing)))
        {
            logger.LogWarning(
                "Unable to run rollback for paymentRequestId: {paymentRequestId} due to existing transactions in status processing",
                paymentRequest.Id);
            return;
        }

        if (paymentRequestCommands.Count(x =>
                x.Status == CommandStatus.Failed && x.Transaction!.Status == TransactionStatus.Recalled) != 1)
        {
            logger.LogWarning("Unable to run multiple rollback commands for paymentRequestId: {paymentRequestId}",
                paymentRequest.Id);
            return;
        }

        var recalledCommand = paymentRequestCommands.GetRecalledCommandForRollBack();
        var preGeneratedRollbackTransactions =
            (await templatesEngine.PreGenerateRollbackTransactions(paymentRequestCommands)).ToList();
        if (preGeneratedRollbackTransactions.Count == 0) return;

        var rollBackCommands = new List<PaymentRequestCommandEntity>();
        var rollBackTransactions = new List<PaymentTransactionEntity>();

        recalledCommand!.Status = CommandStatus.Executed;
        recalledCommand.UpdatedBy = createdBy;

        foreach (var preGeneratedTransaction in preGeneratedRollbackTransactions)
        {
            var transaction = Mapper.Map<PaymentTransactionEntity>(preGeneratedTransaction);
            transaction.CreatedBy = createdBy;

            var command = new PaymentRequestCommandEntity
            {
                StepName = preGeneratedTransaction.StepName,
                PaymentRequestId = transaction.PaymentRequestId,
                Status = CommandStatus.Placed,
                CreatedBy = createdBy,
                Transaction = transaction,
            };
            rollBackTransactions.Add(transaction);
            rollBackCommands.Add(command);
        }

        var pullFromMerchantCommand = rollBackCommands.Find(x => x.StepName == StepName.PullFromMerchant.ToString());

        if (pullFromMerchantCommand != null)
        {
            var merchantCompany = await companyHttpClient.GetCompanyByIdAsync(paymentRequest.PayeeId!, ctx);

            if (merchantCompany is { MerchantAutomaticPullAllowed: false })
            {
                logger.LogWarning(
                    "Merchant disabled automatic pull operation for his bankaccount. Manual action required");

                await notificationService.MerchantManualAchPullRequired(paymentRequest.Id, ctx,
                    "RollBack processing was failed");
            }
        }

        await unitOfWork.PaymentRequestCommandRepository.Update(recalledCommand, ctx);
        await unitOfWork.PaymentTransactionRepository.InsertRange(rollBackTransactions, ctx);
        await unitOfWork.PaymentRequestCommandRepository.InsertRange(rollBackCommands, ctx);

        await unitOfWork.SaveAsync(ctx);
    }

    public async Task<PaginatedResult<PaymentRequestModel>> GetByFilter(PaymentRequestFilter filter,
        CancellationToken ct)
    {
        if (!string.IsNullOrWhiteSpace(filter.Filter) && filter.Filter.Length >= 3)
        {
            var companies = await companyService.SearchCompaniesByName(filter.Filter, ct);
            if (companies != null && companies.Any())
            {
                var companyIds = companies.Select(c => c.Id).ToList();
                filter.CompanyIds = companyIds;
            }
        }

        var entity = await repository.GetByFilter(filter, ct);
        return Mapper.Map<PaginatedResult<PaymentRequestModel>>(entity);
    }

    public async Task<IEnumerable<PaymentRequestModel>> GetByPayableId(string payableId, CancellationToken ct)
    {
        var entities = await repository.GetByPayableId(payableId, ct);

        if (!entities.Any())
            throw new PaymentRequestDoesNotExistException(
                $"Payment request with payable of such id {payableId} was not found");

        return Mapper.Map<IEnumerable<PaymentRequestModel>>(entities);
    }

    public async Task<IEnumerable<PaymentRequestModel>> GetByDrawId(Guid drawId, CancellationToken ct)
    {
        var entities = await repository.GetByDrawId(drawId, ct);

        if (!entities.Any())
            throw new PaymentRequestDoesNotExistException(
                $"Payment request with draw of such id {drawId} was not found");

        return Mapper.Map<IEnumerable<PaymentRequestModel>>(entities);
    }

    public async Task<PaymentRequestModel> CancelPaymentRequest(Guid id, string updatedBy, CancellationToken ct)
    {
        var paymentRequest = await repository.GetById(id, ct) ??
                             throw new VariableNullException(nameof(PaymentRequestEntity));

        paymentRequest.Status = PaymentRequestStatus.Cancelled;
        paymentRequest.UpdatedBy = updatedBy;

        var transactions = paymentRequest.Transactions;
        var transactionStatusHistoryItems = new List<PaymentTransactionHistoryEntity>();

        transactions.ForEach(transaction =>
        {
            transactionStatusHistoryItems.Add(
                transaction.GetNextTransactionHistory(TransactionStatus.Canceled, updatedBy));
            transaction.Status = TransactionStatus.Canceled;
            transaction.UpdatedBy = updatedBy;
        });

        paymentRequest.PaymentRequestCommands.ForEach(item =>
        {
            item.Status = CommandStatus.Canceled;
            item.UpdatedBy = updatedBy;
        });

        await unitOfWork.PaymentRequestCommandRepository.UpdateRange(paymentRequest.PaymentRequestCommands, ct);
        await unitOfWork.PaymentTransactionRepository.UpdateRange(transactions, ct);
        await unitOfWork.PaymentTransactionHistoryRepository.InsertRange(transactionStatusHistoryItems, ct);
        await unitOfWork.PaymentRequestRepository.Update(paymentRequest, ct);

        await unitOfWork.SaveAsync(ct);

        await operationSyncMessageSender.SendMessage(new ServiceBusMessageBt<SyncOperationMessagePayload>(
            new SyncOperationMessagePayload
            {
                PaymentRequestId = id
            }), ct);

        return Mapper.Map<PaymentRequestModel>(paymentRequest);
    }

    public async Task<List<PaymentRequestModel>> CancelPaymentRequestByDrawId(Guid id, string updatedBy, CancellationToken ct)
    {
        var paymentRequestFilter = new PaymentRequestFilter()
        {
            DrawId = id.ToString(),
            RequestType = PaymentRequestType.DrawDisbursement
        };

        var paymentRequests = await repository.GetByFilter(paymentRequestFilter, ct);

        var paymentRequestsModel = new List<PaymentRequestModel>();

        foreach (var paymentRequest in paymentRequests.Result)
        {
            paymentRequestsModel.Add(await CancelPaymentRequest(paymentRequest.Id, updatedBy, ct));
        }

        return paymentRequestsModel;
    }

    public async Task<PaymentRequestCommandEntity> MarkCommandAsExecuted(Guid id, string updatedBy,
        CancellationToken ct)
    {
        using (logger.BeginScope(new Dictionary<string, object>
               {
                   { "PaymentRequestCommandId", id.ToString() }
               }))
        {
            logger.LogInformation("Start changing command status to executed and its transaction to cleared");

            var command = await unitOfWork
                              .GetById<PaymentRequestCommandEntity>(id, ct,
                                  $"{nameof(PaymentRequestCommandEntity.Transaction)},{nameof(PaymentRequestCommandEntity.PaymentRequest)}") ??
                          throw new VariableNullException(nameof(PaymentRequestCommandEntity));

            if (command.Status is not (CommandStatus.Placed or CommandStatus.Pending))
                throw new CommandMarkAsExecutedException();

            command.Status = CommandStatus.Executed;
            command.UpdatedBy = updatedBy;

            command.Transaction!.Status = TransactionStatus.Cleared;
            command.Transaction.ClearedAt = dateProvider.CurrentDateTime;
            command.Transaction!.UpdatedBy = updatedBy;

            var transactionHistory =
                command.Transaction.GetNextTransactionHistory(TransactionStatus.Cleared, updatedBy);

            await unitOfWork.PaymentTransactionRepository.Update(command.Transaction, ct);
            await unitOfWork.PaymentRequestCommandRepository.Update(command, ct);
            await unitOfWork.PaymentTransactionHistoryRepository.Insert(transactionHistory, ct);

            return command;
        }
    }

    public async Task<PaymentRequestEntity> CommitPaymentRequestChanges(Guid paymentRequestId, string updatedBy,
        CancellationToken ct)
    {
        var paymentRequest = await unitOfWork.PaymentRequestRepository.GetById(paymentRequestId, ct) ??
                             throw new ArgumentNullException(nameof(paymentRequestId),
                                 $"Payment request with id: {paymentRequestId} does not exist.");

        paymentRequest.Status = await CalculatePaymentRequestStatus(paymentRequestId, ct);
        paymentRequest.UpdatedBy = updatedBy;

        await unitOfWork.PaymentRequestRepository.Update(paymentRequest, ct);
        await unitOfWork.SaveAsync(ct);

        await operationSyncMessageSender.SendMessage(new ServiceBusMessageBt<SyncOperationMessagePayload>(
            new SyncOperationMessagePayload
            {
                PaymentRequestId = paymentRequestId
            }), ct);

        return paymentRequest;
    }

    public async Task<PaymentRequestStatus> CalculatePaymentRequestStatus(Guid paymentRequestId, CancellationToken ctx)
    {
        var paymentRequestCommands = await unitOfWork.GetCommandsByPaymentRequestId(paymentRequestId, ctx);
        var mappedCommands = mapper.Map<List<PaymentRequestCommandModel>>(paymentRequestCommands);

        return CalculatePaymentRequestStatus(mappedCommands);
    }

    public PaymentRequestStatus CalculatePaymentRequestStatus(List<PaymentRequestCommandModel> paymentRequestCommands)
    {
        var template = _flowTemplates.Find(x =>
            paymentRequestCommands[0].PaymentRequest!.FlowTemplateCode == x.FlowTemplateCode!);
        if (template is null) throw new PaymentValidationException("Unable to find specified template id");

        if (paymentRequestCommands is null ||
            paymentRequestCommands.Count == 0 ||
            paymentRequestCommands.Exists(x => x.PaymentRequest is null || x.Transaction is null))
            throw new ArgumentNullException(nameof(paymentRequestCommands));

        if (paymentRequestCommands[0].PaymentRequest!.Status == PaymentRequestStatus.Cancelled)
            return PaymentRequestStatus.Cancelled;

        if (paymentRequestCommands.Exists(x => x.Status is (CommandStatus.Failed) &&
                                               x.Transaction!.Status is (TransactionStatus.Error
                                                   or TransactionStatus.Failed)))
            return PaymentRequestStatus.Failed;

        var rollBackCommand = paymentRequestCommands.Find(x => x.Transaction!.Status == TransactionStatus.Recalled);
        if (rollBackCommand != null)
        {
            return DoRollbackCommand(paymentRequestCommands, template, rollBackCommand);
        }

        if (paymentRequestCommands.Exists(x => x.Status is CommandStatus.Executing or CommandStatus.Pending))
            return PaymentRequestStatus.Processing;

        if (paymentRequestCommands.TrueForAll(x =>
                x.Status is CommandStatus.Placed && x.Transaction!.Status is TransactionStatus.Placed))
            return PaymentRequestStatus.Requested;

        var areSettled = template.Steps.All(step =>
        {
            return paymentRequestCommands.Exists(command =>
                command.StepName.Equals(step.Name) && command is
                { Transaction.Status: TransactionStatus.Cleared, Status: CommandStatus.Executed });
        });

        return areSettled ? PaymentRequestStatus.Settled : PaymentRequestStatus.Processing;
    }

    private static PaymentRequestStatus DoRollbackCommand(List<PaymentRequestCommandModel> paymentRequestCommands,
        FlowTemplate template,
        PaymentRequestCommandModel rollBackCommand)
    {
        var stepsWithoutRollback = template
            .Steps
            .Where(x => x.Name != null && !x.Name.Equals(rollBackCommand.StepName))
            .ToList();

        var clearedRequiredSteps = paymentRequestCommands
            .Where(x =>
                stepsWithoutRollback.Exists(y => y.Name!.Equals(x.StepName)) &&
                x.Transaction!.Status == TransactionStatus.Cleared && x.Status == CommandStatus.Executed)
            .ToList();

        if (clearedRequiredSteps.Count == 0) return PaymentRequestStatus.Aborted;

        var oppositeTransactionsCleared = clearedRequiredSteps.TrueForAll(x =>
            paymentRequestCommands.Exists(y =>
                y.StepName.Equals(Enum.Parse<StepName>(x.StepName).GetReversalStepName().ToString()) &&
                y.Status == CommandStatus.Executed && y.Transaction!.Status == TransactionStatus.Cleared)
        );

        return oppositeTransactionsCleared ? PaymentRequestStatus.Aborted : PaymentRequestStatus.Processing;
    }

    public async Task<PaymentRequestModel?> UpdatePauseStatus(Guid paymentRequestId, PausePaymentRequestModel request,
        string updatedBy, CancellationToken ctx)
    {
        var paymentRequest = await unitOfWork.GetById<PaymentRequestEntity>(paymentRequestId, ctx,
            nameof(PaymentRequestEntity.PaymentRequestDetails));

        if (paymentRequest == null)
            return null;

        if (!request.IsPaused && paymentRequest.PaymentRequestDetails == null)
            return mapper.Map<PaymentRequestModel>(paymentRequest);

        var details = paymentRequest.PaymentRequestDetails ??= new PaymentRequestDetailsEntity();

        if (request.IsPaused)
        {
            details.PauseReason = request.PauseReason;
            details.PauseComments = request.PauseComments;
        }

        details.IsPaused = request.IsPaused;
        details.PauseCreatedBy = updatedBy;
        details.PaymentRequestId = paymentRequest.Id;

        if (details.Id != Guid.Empty)
            await unitOfWork.PaymentRequestDetailsRepository.Update(details, ctx);
        else
            await unitOfWork.PaymentRequestDetailsRepository.Insert(details, ctx);

        await unitOfWork.SaveAsync(ctx);

        return mapper.Map<PaymentRequestModel>(paymentRequest);
    }

    public Task<decimal> GetTotalAmount(bool includeProcessingStatus, CancellationToken ct)
    {
        return repository.GetTotalAmount(includeProcessingStatus, ct);
    }
    public async Task ApprovePaymentRequest(Guid paymentRequestId, string confirmedBy, PaymentApprovalRequest request, CancellationToken ct)
    {
        PaymentRequestType[] allowedForPaymentMethodChanging = [PaymentRequestType.FinalPayment, PaymentRequestType.FinalPaymentV2,
            PaymentRequestType.FactoringFinalPayment, PaymentRequestType.FactoringDisbursement, PaymentRequestType.DrawDisbursement];

        var paymentRequestEntity = await unitOfWork.GetById<PaymentRequestEntity>(paymentRequestId, ct, $"{nameof(PaymentRequestEntity.Transactions)},{nameof(PaymentRequestEntity.PaymentRequestCommands)}");

        if (paymentRequestEntity is null)
        {
            logger.LogError($"Сould not find payment request by id {paymentRequestId}");
            throw new VariableNullException(nameof(PaymentRequestEntity));
        }

        if (paymentRequestEntity.ConfirmationType != ConfirmationType.Manual)
        {
            logger.LogError($"Payment request {paymentRequestId} is not manual");
            throw new PaymentValidationException($"Payment request {paymentRequestId} is not manual");
        }

        paymentRequestEntity.ConfirmedAt = DateTime.UtcNow;
        paymentRequestEntity.ConfirmedBy = confirmedBy;

        // Assign sequence number for custom ordering if enabled
        await sequenceNumberService.AssignSequenceNumber(paymentRequestEntity, ct);

        // Set the payment method if it's provided
        if (request.PaymentMethod is not null && paymentRequestEntity.PaymentMethod != request.PaymentMethod &&
            allowedForPaymentMethodChanging.Contains(paymentRequestEntity.RequestType))
        {
            paymentRequestEntity.PaymentMethod = request.PaymentMethod.Value;

            var outTransactions = paymentRequestEntity.Transactions
                .Where(x => x.TransactionType.IsPushTransaction())
                .OrderByDescending(x => x.SequenceNumber)
                .ThenByDescending(x => x.CreatedAt)
                .ToList();

            var lastOutTransaction = outTransactions.FirstOrDefault();

            if (lastOutTransaction != null)
            {
                lastOutTransaction.TransactionType = MapPaymentMethodToTransactionType(request.PaymentMethod.Value);
                lastOutTransaction.PaymentMethod = request.PaymentMethod.Value;

                logger.LogInformation($"Updated transaction type for transaction ID {lastOutTransaction.Id} to {lastOutTransaction.TransactionType}");
            }
            else
            {
                logger.LogWarning($"No outgoing transaction found for payment request {paymentRequestId}");
            }
        }

        await unitOfWork.UpdatePaymentRequest(paymentRequestEntity, ct);
    }

    public async Task<PaymentRequestModel> MarkPaymentRequestAsSucceeded(Guid paymentRequestId, MarkPaymentRequestSucceededModel request, string updatedBy, CancellationToken ct)
    {
        using (logger.BeginScope(new Dictionary<string, object>
               {
                   { "PaymentRequestId", paymentRequestId.ToString() }
               }))
        {
            logger.LogInformation("Start marking payment request as succeeded with status Settled");

            var paymentRequest = await unitOfWork.PaymentRequestRepository.GetById(paymentRequestId, ct) ??
                                 throw new VariableNullException(nameof(PaymentRequestEntity));

            if (paymentRequest.Status is not (PaymentRequestStatus.Requested or
                PaymentRequestStatus.Failed or
                PaymentRequestStatus.Aborted or
                PaymentRequestStatus.Cancelled))
            {
                throw new PaymentValidationException(
                    $"Payment request with id {paymentRequestId} cannot be marked as succeeded because it is in {paymentRequest.Status} status.");
            }

            var commands = await unitOfWork.Get<PaymentRequestCommandEntity>(ct,
                x => x.PaymentRequestId == paymentRequestId,
                null,
                $"{nameof(PaymentRequestCommandEntity.Transaction)}");

            var commandsList = commands.ToList();
            var transactionHistoryItems = new List<PaymentTransactionHistoryEntity>();

            foreach (var command in commandsList)
            {
                command.Status = CommandStatus.Executed;
                command.UpdatedBy = updatedBy;

                if (command.Transaction != null)
                {
                    var oldStatus = command.Transaction.Status;
                    command.Transaction.Status = TransactionStatus.Cleared;
                    command.Transaction.ClearedAt = dateProvider.CurrentDateTime;
                    command.Transaction.UpdatedBy = updatedBy;

                    if (!string.IsNullOrEmpty(request.TransactionReferenceNumber))
                    {
                        command.Transaction.ReferenceNumber = request.TransactionReferenceNumber;
                    }

                    // Add transaction history entry
                    var transactionHistory = command.Transaction.GetNextTransactionHistory(TransactionStatus.Cleared, updatedBy);
                    transactionHistoryItems.Add(transactionHistory);

                    logger.LogInformation("Updated transaction {TransactionId} from {OldStatus} to Cleared",
                        command.Transaction.Id, oldStatus);
                }
            }

            paymentRequest.Status = PaymentRequestStatus.Settled;
            paymentRequest.UpdatedBy = updatedBy;

            if (paymentRequest.ConfirmationType == ConfirmationType.Manual)
            {
                paymentRequest.ConfirmedAt = dateProvider.CurrentDateTime;
                paymentRequest.ConfirmedBy = updatedBy;
            }

            await unitOfWork.PaymentRequestCommandRepository.UpdateRange(commandsList, ct);
            await unitOfWork.PaymentTransactionRepository.UpdateRange(commandsList.Select(c => c.Transaction!), ct);
            await unitOfWork.PaymentTransactionHistoryRepository.InsertRange(transactionHistoryItems, ct);
            await unitOfWork.PaymentRequestRepository.Update(paymentRequest, ct);

            await unitOfWork.SaveAsync(ct);

            // Send sync message
            await operationSyncMessageSender.SendMessage(new ServiceBusMessageBt<SyncOperationMessagePayload>(
                new SyncOperationMessagePayload
                {
                    PaymentRequestId = paymentRequestId
                }), ct);

            logger.LogInformation("Successfully marked payment request {PaymentRequestId} as succeeded. Note: {Note}",
                paymentRequestId, request.Note);

            return Mapper.Map<PaymentRequestModel>(paymentRequest);
        }
    }

    private static PaymentTransactionType MapPaymentMethodToTransactionType(PaymentMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentMethod.Ach => PaymentTransactionType.AchPush,
            PaymentMethod.SameDayAch => PaymentTransactionType.AchPush,
            PaymentMethod.Instant => PaymentTransactionType.InstantPush,
            PaymentMethod.Wire => PaymentTransactionType.WirePush,
            _ => PaymentTransactionType.AchPush
        };
    }
}
