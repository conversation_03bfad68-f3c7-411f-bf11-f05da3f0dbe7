using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.IhcRepayment;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Strategies;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;

namespace BlueTape.PaymentService.CompatibilityService.Strategies;

public class IhcRepaymentCompatibilityStrategy(IIhcRepaymentOperationSyncService operationSyncService) : ICompatibilityStrategy
{
    public Task PerformOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        return operationSyncService.PerformOperation(paymentRequest, cancellationToken);
    }

    public Task SyncOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        return operationSyncService.SyncOperation(paymentRequest, cancellationToken);
    }

    public bool IsApplicable(string templateCode)
    {
        return templateCode is DomainConstants.IhcRepayment;
    }
}
