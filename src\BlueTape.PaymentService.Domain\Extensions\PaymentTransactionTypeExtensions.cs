using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Domain.Extensions;

public static class PaymentTransactionTypeExtensions
{
    /// <summary>
    /// Checks if the transaction type is a push transaction type (AchPush, WirePush, or InstantPush).
    /// </summary>
    /// <param name="transactionType">The transaction type to check</param>
    /// <returns>True if the transaction type is a push type, otherwise false</returns>
    public static bool IsPushTransaction(this PaymentTransactionType transactionType)
    {
        return transactionType is PaymentTransactionType.AchPush
            or PaymentTransactionType.WirePush
            or PaymentTransactionType.InstantPush;
    }
}
