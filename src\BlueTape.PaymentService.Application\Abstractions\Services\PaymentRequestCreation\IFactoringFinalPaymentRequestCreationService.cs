﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringFinalPayment;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IFactoringFinalPaymentRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(FactoringFinalPaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
