using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models.Configuration;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace BlueTape.PaymentService.Application.Services;

/// <summary>
/// Service for managing custom ordering configuration
/// </summary>
public class CustomOrderingConfigService(
    IPaymentConfigRepository paymentConfigRepository,
    IConfiguration configuration,
    IDateProvider dateProvider,
    ILogger<CustomOrderingConfigService> logger) : ICustomOrderingConfigService
{
    private const string CustomOrderingConfigKey = "CUSTOM_ORDERING_CONFIG";

    public async Task<CustomOrderingConfig> GetCustomOrderingConfig(CancellationToken ct)
    {
        try
        {
            // First try to get from database
            var configEntity = await paymentConfigRepository.GetByConfigKey(CustomOrderingConfigKey, ct);
            if (configEntity != null)
            {
                var config = JsonSerializer.Deserialize<CustomOrderingConfig>(configEntity.ConfigValue);
                if (config != null)
                {
                    logger.LogDebug("Custom ordering configuration loaded from database");
                    return config;
                }
            }

            // Fall back to configuration file
            var defaultConfig = GetDefaultConfigFromAppSettings();
            logger.LogDebug("Custom ordering configuration loaded from appsettings");
            return defaultConfig;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error loading custom ordering configuration, using default");
            return GetDefaultConfig();
        }
    }

    public async Task UpdateCustomOrderingConfig(CustomOrderingConfig config, string updatedBy, CancellationToken ct)
    {
        try
        {
            config.UpdatedAt = dateProvider.CurrentDateTime;
            config.UpdatedBy = updatedBy;

            var configJson = JsonSerializer.Serialize(config);
            var configEntity = await paymentConfigRepository.GetByConfigKey(CustomOrderingConfigKey, ct);

            if (configEntity == null)
            {
                configEntity = new PaymentConfigEntity
                {
                    ConfigKey = CustomOrderingConfigKey,
                    ConfigValue = configJson,
                    Description = "Custom ordering configuration for payment processing",
                    CreatedAt = dateProvider.CurrentDateTime,
                    CreatedBy = updatedBy
                };
                await paymentConfigRepository.Add(configEntity, ct);
            }
            else
            {
                configEntity.ConfigValue = configJson;
                configEntity.UpdatedAt = dateProvider.CurrentDateTime;
                configEntity.UpdatedBy = updatedBy;
                await paymentConfigRepository.Update(configEntity, ct);
            }

            logger.LogInformation("Custom ordering configuration updated by {UpdatedBy}", updatedBy);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating custom ordering configuration");
            throw;
        }
    }

    public async Task<bool> IsCustomOrderingSupported(PaymentRequestType paymentRequestType, PaymentMethod paymentMethod, CancellationToken ct)
    {
        var config = await GetCustomOrderingConfig(ct);
        
        return config.IsCustomOrderingEnabled &&
               config.SupportedPaymentTypes.Contains(paymentRequestType) &&
               config.SupportedPaymentMethods.Contains(paymentMethod);
    }

    public async Task ToggleCustomOrdering(bool enabled, string updatedBy, CancellationToken ct)
    {
        var config = await GetCustomOrderingConfig(ct);
        config.IsCustomOrderingEnabled = enabled;
        await UpdateCustomOrderingConfig(config, updatedBy, ct);
        
        logger.LogInformation("Custom ordering {Status} by {UpdatedBy}", 
            enabled ? "enabled" : "disabled", updatedBy);
    }

    private CustomOrderingConfig GetDefaultConfigFromAppSettings()
    {
        var config = new CustomOrderingConfig();
        configuration.GetSection("CustomOrderingConfig").Bind(config);
        return config;
    }

    private static CustomOrderingConfig GetDefaultConfig()
    {
        return new CustomOrderingConfig
        {
            IsCustomOrderingEnabled = false,
            SupportedPaymentTypes = new List<PaymentRequestType>
            {
                PaymentRequestType.FactoringDisbursement,
                PaymentRequestType.FactoringFinalPayment,
                PaymentRequestType.DrawDisbursement,
                PaymentRequestType.FinalPayment,
                PaymentRequestType.FinalPaymentV2
            },
            SupportedPaymentMethods = new List<PaymentMethod>
            {
                PaymentMethod.Instant,
                PaymentMethod.Wire,
                PaymentMethod.SameDayAch,
                PaymentMethod.Ach
            },
            AutoAssignSequenceNumbers = true,
            MaxReorderBatchSize = 100
        };
    }
}
