using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Entities;

namespace BlueTape.PaymentService.CompatibilityService.Abstractions.Service.IhcRepayment;

public interface IIhcRepaymentCompatibilityMapper
{
    OperationEntity MapFromPaymentRequestToOperation(PaymentRequestEntity paymentRequest, string ownerId);

    Task<List<TransactionEntity>> MapFromPaymentTransactionsToLegacyTransactions(PaymentRequestEntity paymentRequest,
        OperationEntity operation, CancellationToken ctx);

    SyncTransactionModel MapPaymentTransactionToSyncModel(PaymentTransactionEntity transaction);

    Task<UpdateTransactionEntity> MapSyncModelToUpdateTransactionEntity(SyncTransactionModel syncTransactionModel, CancellationToken ctx);
}
