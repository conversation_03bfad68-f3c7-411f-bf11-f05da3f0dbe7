using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.PaymentService.UnitTests.Services;

public class UnifiedPaymentCreationServiceTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IPaymentFlowTemplatesEngine> _templatesEngineMock;
    private readonly Mock<IInvoiceHttpClient> _invoiceHttpClientMock;
    private readonly Mock<ILogger<UnifiedPaymentCreationService>> _loggerMock;
    private readonly Mock<IOperationSyncMessageSender> _operationSyncMessageSenderMock;
    private readonly Mock<IPaymentRequestPayableService> _paymentRequestPayableServiceMock;
    private readonly Mock<ILoanManagementService> _loanManagementServiceMock;
    private readonly Mock<IPaymentCreationStrategy> _invoiceStrategyMock;
    private readonly Mock<IPaymentCreationStrategy> _drawRepaymentStrategyMock;
    private readonly UnifiedPaymentCreationService _service;

    public UnifiedPaymentCreationServiceTests()
    {
        _mapperMock = new Mock<IMapper>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _templatesEngineMock = new Mock<IPaymentFlowTemplatesEngine>();
        _invoiceHttpClientMock = new Mock<IInvoiceHttpClient>();
        _loggerMock = new Mock<ILogger<UnifiedPaymentCreationService>>();
        _operationSyncMessageSenderMock = new Mock<IOperationSyncMessageSender>();
        _paymentRequestPayableServiceMock = new Mock<IPaymentRequestPayableService>();
        _loanManagementServiceMock = new Mock<ILoanManagementService>();
        _invoiceStrategyMock = new Mock<IPaymentCreationStrategy>();
        _drawRepaymentStrategyMock = new Mock<IPaymentCreationStrategy>();

        // Setup strategy mocks
        _invoiceStrategyMock.Setup(s => s.CanHandle(DomainConstants.InvoicePayment)).Returns(true);
        _invoiceStrategyMock.Setup(s => s.CanHandle(DomainConstants.InvoicePaymentV2)).Returns(true);
        _drawRepaymentStrategyMock.Setup(s => s.CanHandle(DomainConstants.DrawRepayment)).Returns(true);

        var strategies = new List<IPaymentCreationStrategy>
        {
            _invoiceStrategyMock.Object,
            _drawRepaymentStrategyMock.Object
        };

        _service = new UnifiedPaymentCreationService(
            _mapperMock.Object,
            _unitOfWorkMock.Object,
            _templatesEngineMock.Object,
            _invoiceHttpClientMock.Object,
            _loggerMock.Object,
            _operationSyncMessageSenderMock.Object,
            _paymentRequestPayableServiceMock.Object,
            _loanManagementServiceMock.Object,
            strategies);
    }

    [Fact]
    public async Task CreatePaymentRequest_WithInvoicePayment_ShouldUseInvoiceStrategy()
    {
        // Arrange
        var createPaymentRequest = new CreatePaymentRequestModel
        {
            FlowTemplateCode = DomainConstants.InvoicePayment,
            RequestedAmount = 100m,
            Currency = "USD"
        };

        _invoiceStrategyMock
            .Setup(s => s.ProcessAdditionalLogic(It.IsAny<CreatePaymentRequestModel>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act & Assert - This will test that the correct strategy is selected
        // The actual creation logic is tested in the base class, so we're focusing on strategy selection
        var exception = await Assert.ThrowsAsync<NotImplementedException>(
            () => _service.CreatePaymentRequest(createPaymentRequest, "test-user", CancellationToken.None));

        // Verify the correct strategy was called
        _invoiceStrategyMock.Verify(s => s.ProcessAdditionalLogic(createPaymentRequest, It.IsAny<CancellationToken>()), Times.Once);
        _drawRepaymentStrategyMock.Verify(s => s.ProcessAdditionalLogic(It.IsAny<CreatePaymentRequestModel>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task CreatePaymentRequest_WithDrawRepayment_ShouldUseDrawRepaymentStrategy()
    {
        // Arrange
        var createPaymentRequest = new CreatePaymentRequestModel
        {
            FlowTemplateCode = DomainConstants.DrawRepayment,
            RequestedAmount = 200m,
            Currency = "USD"
        };

        _drawRepaymentStrategyMock
            .Setup(s => s.ProcessAdditionalLogic(It.IsAny<CreatePaymentRequestModel>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotImplementedException>(
            () => _service.CreatePaymentRequest(createPaymentRequest, "test-user", CancellationToken.None));

        // Verify the correct strategy was called
        _drawRepaymentStrategyMock.Verify(s => s.ProcessAdditionalLogic(createPaymentRequest, It.IsAny<CancellationToken>()), Times.Once);
        _invoiceStrategyMock.Verify(s => s.ProcessAdditionalLogic(It.IsAny<CreatePaymentRequestModel>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task CreatePaymentRequest_WithUnsupportedFlowTemplate_ShouldThrowException()
    {
        // Arrange
        var createPaymentRequest = new CreatePaymentRequestModel
        {
            FlowTemplateCode = "UNSUPPORTED_TEMPLATE",
            RequestedAmount = 100m,
            Currency = "USD"
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.CreatePaymentRequest(createPaymentRequest, "test-user", CancellationToken.None));

        Assert.Contains("No strategy found for flow template code: UNSUPPORTED_TEMPLATE", exception.Message);
    }

    [Theory]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.DrawRepayment)]
    public void GetStrategy_WithSupportedFlowTemplates_ShouldReturnCorrectStrategy(string flowTemplateCode)
    {
        // Arrange
        var createPaymentRequest = new CreatePaymentRequestModel
        {
            FlowTemplateCode = flowTemplateCode,
            RequestedAmount = 100m,
            Currency = "USD"
        };

        // Act & Assert - This should not throw an exception
        var exception = Record.Exception(() => 
        {
            // We can't directly test GetStrategy as it's private, but we can test through CreatePaymentRequest
            _service.CreatePaymentRequest(createPaymentRequest, "test-user", CancellationToken.None);
        });

        // The method should find a strategy (even though it will fail later due to mocking)
        Assert.IsNotType<InvalidOperationException>(exception);
    }
}
