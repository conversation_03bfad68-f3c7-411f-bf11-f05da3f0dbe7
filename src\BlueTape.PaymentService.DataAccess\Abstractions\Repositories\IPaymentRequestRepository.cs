﻿using BlueTape.PaymentService.DataAccess.Abstractions.Repositories.Base;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Base;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.DataAccess.Abstractions.Repositories;

public interface IPaymentRequestRepository : IGenericRepository<PaymentRequestEntity>
{
    Task<List<PaymentRequestEntity>> GetByIds(IEnumerable<Guid> ids, CancellationToken ct, bool hasNoTracking = false);
    Task<PaginatedEntityResult<PaymentRequestEntity>> GetByFilter(PaymentRequestFilter filter, CancellationToken ct, bool hasNoTracking = false);
    Task<IEnumerable<PaymentRequestEntity>> GetByPayableId(string payableId, CancellationToken ct, bool hasNoTracking = false);
    Task<List<PaymentRequestEntity>> GetByDrawId(Guid? lmsId, CancellationToken ct);
    Task<decimal> GetTotalAmount(bool includeProcessingStatus, CancellationToken ct);

    // Custom ordering methods
    Task<int> GetMaxSequenceNumber(PaymentMethod paymentMethod, PaymentRequestType paymentRequestType, PaymentRequestStatus status, CancellationToken ct);
    Task<List<PaymentRequestEntity>> GetNewlyApprovedPayments(PaymentMethod paymentMethod, PaymentRequestType paymentRequestType, List<Guid> excludePaymentIds, CancellationToken ct);
    Task<int> ResetAllSequenceNumbers(string updatedBy, DateTime updatedAt, CancellationToken ct);
}