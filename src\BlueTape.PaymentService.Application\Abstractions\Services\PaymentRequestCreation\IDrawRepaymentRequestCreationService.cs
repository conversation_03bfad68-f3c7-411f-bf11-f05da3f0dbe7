﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IDrawRepaymentRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(DrawRepaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
