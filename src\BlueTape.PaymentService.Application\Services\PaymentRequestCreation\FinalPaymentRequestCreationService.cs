﻿using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

public class FinalPaymentRequestCreationService(
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IInvoiceHttpClient invoiceHttpClient,
    IPaymentFlowTemplatesEngine templatesEngine,
    ILogger<FinalPaymentRequestCreationService> logger,
    IOperationSyncMessageSender operationSyncMessageSender,
    IPaymentRequestPayableService paymentRequestPayableService,
    IPaymentRequestValidator validator,
    ILoanManagementService loanManagementService) : BasePaymentRequestCreationService(
    mapper, unitOfWork, templatesEngine, invoiceHttpClient, logger, operationSyncMessageSender, paymentRequestPayableService, loanManagementService), IFinalPaymentRequestCreationService
{
    public Task<PaymentRequestModel> Add(FinalPaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct)
    {
        using (logger.BeginScope(new Dictionary<string, object>
               {
                   { "FlowTemplateCode", paymentRequestMessage.FlowTemplateCode },
                   { "SellerCompanyId", paymentRequestMessage.PaymentRequestDetails.SellerDetails.CompanyId },
                   { "PaymentMethod", paymentRequestMessage.PaymentRequestDetails.PaymentMethod },
               }))
        {
            logger.LogInformation("Create payment request started. Payload: {@paymentRequestMessage}", paymentRequestMessage);

            var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(paymentRequestMessage);

            var merchantFees = GetMerchantFeesFromPayablesDiscount(createPaymentRequest.PaymentRequestPayables,
                paymentRequestMessage.PaymentRequestDetails.SellerDetails.CompanyId, FeeType.Merchant);

            if (merchantFees is not null && merchantFees.Any())
                createPaymentRequest.PaymentRequestFees.AddRange(merchantFees);

            return Add(createPaymentRequest, createdBy, ct);
        }
    }

    protected override Task SendNotification(CreatePaymentRequestModel createPaymentRequest, PaymentRequestEntity paymentRequestEntity,
        IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct)
    {
        // ToDo: ledger, notifications
        return Task.CompletedTask;
    }

    protected override Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices,
        List<PaymentRequestPayableModel> processedPayables, LoanDto? loan, CancellationToken ct)
    {
        return validator.ValidateSupplierDisbursementRequest(createPaymentRequest, existingInvoices, processedPayables, ct);
    }
}
