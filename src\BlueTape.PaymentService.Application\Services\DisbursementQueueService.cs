using AutoMapper;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services;

/// <summary>
/// Service for managing disbursement queues and custom ordering
/// </summary>
public class DisbursementQueueService(
    IPaymentJobProcessor paymentJobProcessor,
    ISequenceNumberService sequenceNumberService,
    IPaymentRequestRepository paymentRequestRepository,
    ICustomOrderingConfigService customOrderingConfigService,
    IDateProvider dateProvider,
    I<PERSON>ap<PERSON> mapper,
    ILogger<DisbursementQueueService> logger) : IDisbursementQueueService
{
    public async Task<List<DisbursementQueueViewModel>> GetDisbursementQueues(string provider, PaymentSubscriptionType subscriptionCode, CancellationToken ct)
    {
        try
        {
            // Get commands to execute from payment job processor
            var (_, commandsToExecute) = await paymentJobProcessor.GetDisbursementQueuesPaymentRequests(ct);

            // Filter by subscription
            var filteredCommands = commandsToExecute
                .Where(x => x.PaymentRequest != null && x.PaymentRequest.PaymentSubscription == subscriptionCode)
                .ToList();

            // Group by payment method
            var groupedByPaymentMethod = filteredCommands
                .GroupBy(x => x.PaymentRequest!.PaymentMethod)
                .OrderBy(g => GetPaymentMethodPriority(g.Key))
                .ToList();

            var queues = new List<DisbursementQueueViewModel>();

            foreach (var group in groupedByPaymentMethod)
            {
                var paymentMethod = group.Key;
                var commands = group.ToList();

                // Sort commands within each queue
                var sortedCommands = await SortCommandsForQueue(commands, ct);

                var queueItems = new List<DisbursementQueueItemViewModel>();
                var rowNumber = 1;

                foreach (var command in sortedCommands)
                {
                    var paymentRequest = command.PaymentRequest!;
                    queueItems.Add(new DisbursementQueueItemViewModel
                    {
                        Id = paymentRequest.Id,
                        RowNumber = rowNumber++,
                        Amount = paymentRequest.Amount,
                        FeeAmount = paymentRequest.FeeAmount,
                        Currency = paymentRequest.Currency,
                        RequestType = paymentRequest.RequestType,
                        PaymentMethod = paymentRequest.PaymentMethod,
                        PayerId = paymentRequest.PayerId,
                        PayeeId = paymentRequest.PayeeId,
                        ConfirmedAt = paymentRequest.ConfirmedAt,
                        ConfirmedBy = paymentRequest.ConfirmedBy,
                        CreatedAt = paymentRequest.CreatedAt,
                        Status = paymentRequest.Status,
                        SequenceNumber = paymentRequest.SequenceNumber
                    });
                }

                queues.Add(new DisbursementQueueViewModel
                {
                    PaymentMethod = paymentMethod,
                    QueueName = GetQueueDisplayName(paymentMethod),
                    PaymentRequests = queueItems,
                    TotalCount = queueItems.Count,
                    TotalAmount = queueItems.Sum(x => x.Amount)
                });
            }

            logger.LogInformation("Retrieved {QueueCount} disbursement queues with {TotalCount} payment requests for {Provider}/{Subscription}",
                queues.Count, queues.Sum(q => q.TotalCount), provider, subscriptionCode);

            return queues;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving disbursement queues for {Provider}/{Subscription}", provider, subscriptionCode);
            throw;
        }
    }

    public async Task<UpdateSequenceOrderResponseViewModel> UpdateSequenceOrder(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct)
    {
        try
        {
            if (!paymentRequestIds.Any())
            {
                throw new ArgumentException("Payment request IDs list cannot be empty", nameof(paymentRequestIds));
            }

            var config = await customOrderingConfigService.GetCustomOrderingConfig(ct);
            if (!config.IsCustomOrderingEnabled)
            {
                throw new InvalidOperationException("Custom ordering is not enabled");
            }

            var updatedCount = await sequenceNumberService.UpdateSequenceNumbers(paymentRequestIds, updatedBy, ct);

            // Get the final list of payment requests (including newly approved ones)
            var finalPaymentRequests = await paymentRequestRepository.GetByIds(paymentRequestIds, ct, hasNoTracking: true);

            logger.LogInformation("Updated sequence order for {UpdatedCount} payment requests by {UpdatedBy}", updatedCount, updatedBy);

            return new UpdateSequenceOrderResponseViewModel
            {
                UpdatedCount = updatedCount,
                Message = $"Successfully updated sequence order for {updatedCount} payment requests",
                ProcessedPaymentRequestIds = finalPaymentRequests.Select(p => p.Id).ToList()
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating sequence order for payment requests by {UpdatedBy}", updatedBy);
            throw;
        }
    }

    public async Task UnapprovePaymentRequest(Guid paymentRequestId, string updatedBy, CancellationToken ct)
    {
        try
        {
            var paymentRequest = await paymentRequestRepository.GetById(paymentRequestId, ct);
            if (paymentRequest == null)
            {
                throw new PaymentRequestDoesNotExistException($"Payment request {paymentRequestId} not found");
            }

            // Validate that the payment can be unapproved
            if (paymentRequest.Status != PaymentRequestStatus.Requested)
            {
                throw new PaymentValidationException($"Payment request {paymentRequestId} cannot be unapproved because it is in {paymentRequest.Status} status");
            }

            if (paymentRequest.ConfirmedAt == null)
            {
                throw new PaymentValidationException($"Payment request {paymentRequestId} is not approved");
            }

            // Check if payment has already started processing
            var hasProcessingCommands = paymentRequest.PaymentRequestCommands
                .Any(c => c.Status == CommandStatus.Executing || c.Status == CommandStatus.Pending);

            if (hasProcessingCommands)
            {
                throw new PaymentValidationException($"Payment request {paymentRequestId} cannot be unapproved because it has already started processing");
            }

            // Unapprove the payment
            paymentRequest.ConfirmedAt = null;
            paymentRequest.ConfirmedBy = null;
            paymentRequest.SequenceNumber = 0; // Reset sequence number
            paymentRequest.UpdatedAt = dateProvider.CurrentDateTime;
            paymentRequest.UpdatedBy = updatedBy;

            await paymentRequestRepository.Update(paymentRequest, ct);

            logger.LogInformation("Payment request {PaymentRequestId} unapproved by {UpdatedBy}", paymentRequestId, updatedBy);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error unapproving payment request {PaymentRequestId} by {UpdatedBy}", paymentRequestId, updatedBy);
            throw;
        }
    }

    private async Task<List<Domain.Entities.PaymentRequestCommandEntity>> SortCommandsForQueue(
        List<Domain.Entities.PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        var config = await customOrderingConfigService.GetCustomOrderingConfig(ct);
        
        if (config.IsCustomOrderingEnabled)
        {
            // Sort by sequence number first, then by default ordering
            return commands
                .OrderBy(x => x.PaymentRequest!.SequenceNumber == 0 ? int.MaxValue : x.PaymentRequest.SequenceNumber)
                .ThenBy(x => x.PaymentRequest!.ConfirmedAt)
                .ThenBy(x => x.PaymentRequest!.CreatedAt)
                .ToList();
        }
        else
        {
            // Default ordering
            return commands
                .OrderBy(x => x.PaymentRequest!.ConfirmedAt)
                .ThenBy(x => x.PaymentRequest!.CreatedAt)
                .ToList();
        }
    }

    private static int GetPaymentMethodPriority(PaymentMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentMethod.Instant => 1,
            PaymentMethod.Wire => 2,
            PaymentMethod.SameDayAch => 3,
            PaymentMethod.Ach => 4,
            _ => 999
        };
    }

    private static string GetQueueDisplayName(PaymentMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentMethod.Instant => "Instant Transfer Queue",
            PaymentMethod.Wire => "Wire Transfer Queue",
            PaymentMethod.SameDayAch => "Same Day ACH Queue",
            PaymentMethod.Ach => "Regular ACH Queue",
            _ => $"{paymentMethod} Queue"
        };
    }
}
