using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IPaymentCreationStrategy
{
    bool CanHandle(string flowTemplateCode);
    Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct);
    Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct);
    Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct);
}
