﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentCard;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IInvoicePaymentCardRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(InvoicePaymentCardRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
