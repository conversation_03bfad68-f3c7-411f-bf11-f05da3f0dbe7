﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IFactoringDisbursementRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(FactoringDisbursementRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
