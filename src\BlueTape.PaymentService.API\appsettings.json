{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft.AspNetCore": "Warning",
        "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"
      }
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=paymentdb;Username=********;Password=********"
  },
  "SlackNotification": {
    "ErrorSnsTopicName": "payment-service-notifications-dev"
  },
  "S3ConfigurationOptions": {
    "S3BucketName": "uw1.linqpal-user-assets",
    "DwhAssetsBucketName": "dwh-assets",
    "BlueTapeDefaultAwsRegion": "us-west-1"
  },
  "ReportOptions": {
    "BaseReportOptions": {
      "EmailBody": "Settlement reports generation is finished. \n Please check attachments to see results.",
      "EmailSubject": "Settlement reports"
    },
    "AionACHPaymentsSettlement": {
      "FileName": "Aion_Payments_Settlement_Report_",
      "FilePath": "reports/settlement/aionAchPaymentsSettlement",
      "EmailBody": "Aion ACH Payments Settlement report generation is finished. \n Please check attachments to see results.",
      "EmailSubject": "Aion ACH Payments Settlement report"
    },
    "CBWLocDrawDisbursementsSettlement": {
      "FileName": "CBW_Loc_Draw_Disbursements_Settlement_",
      "FilePath": "reports/settlement/cbwLocDrawDisbursementsSettlement",
      "EmailBody": "CBW Loc Draw Disbursements Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "CBW Loc Draw Disbursements Settlement"
    },
    "AionLocDrawDisbursementsSettlement": {
      "FileName": "Aion_Loc_Draw_Disbursements_Settlement_",
      "FilePath": "reports/settlement/aionLocDrawDisbursementsSettlement",
      "EmailBody": "Aion Loc Draw Disbursements Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "Aion Loc Draw Disbursements Settlement"
    },
    "DrawServicingAionSettlement": {
      "FileName": "Draw_Servicing_Aion_Settlement_",
      "FilePath": "reports/settlement/drawServicingAionSettlement",
      "EmailBody": "Draw Servicing Aion Settlement report generation is finished. \n Please check attachments to see results.",
      "EmailSubject": "Draw Servicing Aion Settlement report"
    },
    "DrawServicingCBWSettlement": {
      "FileName": "Draw_Servicing_CBW_Settlement_",
      "FilePath": "reports/settlement/drawServicingCBWSettlement",
      "EmailBody": "Draw Servicing CBW Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "Draw Servicing CBW Settlement"
    },
    "CardPaymentsSettlement": {
      "FileName": "Card_Payments_Settlement_",
      "FilePath": "reports/settlement/cardPaymentsSettlement",
      "EmailBody": "Card Payments Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "Card Payments Settlement Settlement"
    },
    "ArAdvanceDisbursementsSettlement": {
      "FileName": "ArAdvance_Disbursements_Settlement_",
      "FilePath": "reports/settlement/arAdvanceDisbursementsSettlement",
      "EmailBody": "ArAdvance Disbursements Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "ArAdvance Disbursements Settlement"
    },
    "ArAdvanceCustomerPaymentSettlement": {
      "FileName": "ArAdvance_Customer_Payment_Settlement_",
      "FilePath": "reports/settlement/arAdvanceCustomerPaymentSettlement",
      "EmailBody": "ArAdvance Customer Payment Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "ArAdvance Customer Payment Settlement"
    },
    "ArAdvancePortfolioTransaction": {
      "FileName": "ArAdvance_Portfolio_Transaction_",
      "FilePath": "reports/settlement/arAdvancePortfolioTransaction",
      "EmailBody": "ArAdvance Portfolio Transaction report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "ArAdvance Portfolio Transaction"
    },
    "DrawServicingManualPaymentSettlement": {
      "FileName": "Draw_Servicing_Manual_Payment_Settlement_",
      "FilePath": "reports/settlement/drawServicingManualPaymentSettlement",
      "EmailBody": "Draw Servicing Manual Payment Settlement report generation is finished. \n Please check attachments to see results. ",
      "EmailSubject": "Draw Servicing Manual Payment Settlement"
    }
  },
  "EmailReportTemplateOptions": {
    "GenericReportTemplateId": "d-035830ae194446bfadf21ef98ce904eb",
    "Receivers": [ "<EMAIL>" ]
  },
  "NET-AION-SERVICE-API-URL": "https://api-dev.bluetape.com/AionService",
  "NET-INVOICE-SERVICE-API-URL": "https://api-dev.bluetape.com/InvoiceService",
  "NET-COMPANY-SERVICE-API-URL": "https://api-dev.bluetape.com/CompanyService",
  "TwilioTemplates": {
    "Templates": {
      "PaymentRequestProcessing": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        // TODO - need to delete later from send grid - "templateId": "d-626f3e1e802743098020731196e61433",
        "templateId": "d-d5f0a234bf664fac82988f48f2d0e701"
      },
      "PaymentRequestProcessed": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-17c5161dee494eda887519f32d0bb4aa"
      },
      "MultipleAdvanceProcessed": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-2aae7bc7370f458c94a390b0bce7b257"
      },
      "MultipleFinalPayment": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-136342af4a9141979743596a27703402"
      },
      "FinalPaymentProcessing": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-1a3c8074362d49cabe4efb117e15b920"
      },
      "AdvancePaymentProcessing": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-2ee75e21420840c8b2e94808fcea6459"
      },
      "IhcRepaymentProcessing": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-c6427332b69c498a9986d9ed4a2c5f7f"
      },
      "IhcRepaymentFailed": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-8bf392ec15104355b12261daa2a44c6c"
      },
      "IhcRepaymentFailedToOpsTeam": {
        "fromEmail": "<EMAIL>",
        "fromName": "BlueTape Inc",
        "templateId": "d-5f10aa85569f408bbce4cbb3ef9d850c"
      }
    }
  },
  "FlowTemplateOptions": {
    "MerchantAchBusinessDaysDelay": 0,
    "Templates": [
      {
        "FlowTemplateCode": "CREATE.PAYNOW.INVOICE_PAYMENT",
        "PaymentSubscription": "SUBSCRIPTION1",
        "TemplateName": "PayNow ACH Flow",
        "Product": "PayNow",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "PullFromCustomer",
            "TransactionType": "AchPull",
            "ConditionType": "None",
            "DelayDaysType": "None",
            "Condition": null,
            "TransactionAmount": [
              "PaymentRequestAmount",
              "+PurchaserFee",
              "-AchEarlyPaymentDiscount"
            ],
            "OriginatorAccount": "COLLECTION", // to
            "ReceiverAccount": "BORROWER" // from
          },
          {
            "Sequence": 2,
            "Name": "CollectToFunding",
            "TransactionType": "AchInternal",
            "ConditionType": "Wait",
            "Condition": "PullFromCustomer",
            "DelayDaysType": "None",
            "TransactionAmount": [
              "PaymentRequestAmount",
              "-MerchantFee",
              "-AchEarlyPaymentDiscount"
            ],
            "OriginatorAccount": "FUNDING", // to
            "ReceiverAccount": "COLLECTION" // from
          },
          {
            "Sequence": 3,
            "Name": "CollectToRevenue",
            "TransactionType": "AchInternal",
            "ConditionType": "Wait",
            "Condition": "CollectToFunding",
            "DelayDaysType": "None",
            "TransactionAmount": [
              "+PurchaserFee",
              "+MerchantFee"
            ],
            "OriginatorAccount": "REVENUE", // to
            "ReceiverAccount": "COLLECTION" // from
          },
          {
            "Sequence": 4,
            "Name": "PushToMerchant",
            "TransactionType": "AchPush",
            "ConditionType": "Wait",
            "Condition": "CollectToRevenue",
            "DelayDaysType": "MerchantAchDelay",
            "TransactionAmount": [
              "PaymentRequestAmount",
              "-MerchantFee",
              "-AchEarlyPaymentDiscount"
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "FUNDING" // from
          }
        ],
        "RetryPolicy": {
          "NotEnoughBalanceMaxPeriodInDays": 5,
          "TooManyRequestsMaxAttemptsCount": 5
        }
      },
      {
        "FlowTemplateCode": "CREATE.PAYNOW.INVOICE_PAYMENT.CARD",
        "PaymentSubscription": "SUBSCRIPTION1",
        "TemplateName": "PayNow Card Flow",
        "Product": "PayNow",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "MoveCardPaymentToFunding",
            "TransactionType": "AchInternal",
            "ConditionType": "Wait",
            "Condition": "WaitForEligibleBalanceFIFO",
            "DelayDaysType": "oneBusinessDay",
            "TransactionAmount": [
              "PaymentRequestAmount",
              "-MerchantFee"
            ],
            "OriginatorAccount": "FUNDING", // to
            "ReceiverAccount": "CARDCOLLECTION", // from
            "RetryPolicy": "none"
          },
          {
            "Sequence": 2,
            "Name": "PushToMerchant",
            "TransactionType": "AchPush",
            "ConditionType": "wait",
            "Condition": "MoveCardPaymentToFunding",
            "DelayDaysType": "merchantAchDelay",
            "TransactionAmount": [
              "PaymentRequestAmount",
              "-MerchantFee"
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "FUNDING", // from
            "RetryPolicy": "none"
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.DRAW.REPAYMENT",
        "TemplateName": "Draw Repayment Flow",
        "PaymentSubscription": null,
        "Product": "TradeCredit",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "CollectFromBorrower",
            "TransactionType": "AchPull",
            "ConditionType": "None",
            "DelayDaysType": "AchHold",
            "Condition": null,
            "TransactionAmount": [
              "RepaymentAmount"
            ],
            "OriginatorAccount": "DynamicSpvCollection", // to
            "ReceiverAccount": "BORROWER" // from
          }
        ],
        "RetryPolicy": {
          "NotEnoughBalanceMaxPeriodInDays": 5,
          "TooManyRequestsMaxAttemptsCount": 5
        }
      },
      {
        "FlowTemplateCode": "CREATE.DRAW.REPAYMENT.CARD",
        "TemplateName": "Draw Repayment Flow",
        "PaymentSubscription": null,
        "Product": "TradeCredit",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "MoveCardPaymentToCollection",
            "TransactionType": "AchInternal",
            "ConditionType": "Wait",
            "Condition": "WaitForEligibleBalanceFIFO",
            "DelayDaysType": "twoBusinessDays",
            "TransactionAmount": [
              "RepaymentAmount"
            ],
            "OriginatorAccount": "CARDCOLLECTION", // to
            "ReceiverAccount": "DynamicSpvCollection" // from
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.DRAW.REPAYMENT.MANUAL",
        "TemplateName": "Lockbox/DACA Draw Repayment Flow",
        "PaymentSubscription": null,
        "Product": "TradeCredit",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "CollectFromLockboxOrDaca",
            "TransactionType": "AchInternal",
            "ConditionType": "none",
            "Condition": null,
            "DelayDaysType": "none",
            "TransactionAmount": [
              "RepaymentAmount"
            ],
            "OriginatorAccount": "DynamicSpvCollection", // to
            "ReceiverAccount": "MANUALPAYMENT" // from
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.DRAW.FINALPAYMENT",
        "TemplateName": "Draw Final Payment Flow",
        "Product": "TradeCredit",
        "PaymentSubscription": null,
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "PushToMerchantFinalAmount",
            "TransactionType": "AchPush",
            "ConditionType": "None",
            "Condition": "None",
            "DelayDaysType": "None",
            "TransactionAmount": [
              "FinalAmount"
              //"-MerchantFee",
              //"-CustomerFees" //Not include to calculation, only fot information
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "DynamicSpvFunding", // from
            "RetryPolicy": "None"
          }
        ],
        "RetryPolicy": {
          "NotEnoughBalanceMaxPeriodInDays": 5,
          "TooManyRequestsMaxAttemptsCount": 5
        }
      },
      {
        "FlowTemplateCode": "CREATE.DRAW.FINALPAYMENT.V2",
        "TemplateName": "Draw Final Payment Flow",
        "Product": "TradeCredit",
        "PaymentSubscription": null,
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "CollectToFunding",
            "TransactionType": "AchInternal",
            "ConditionType": "None",
            "Condition": null,
            "DelayDaysType": "None",
            "TransactionAmount": [
              "FinalAmount"
              //"-MerchantFee",
              //"-CustomerFees" // Not include to calculation, only fot information
            ],
            "OriginatorAccount": "DynamicSpvFunding", // to
            "ReceiverAccount": "DynamicSpvCollection", // from
            "RetryPolicy": "Exponential4Times"
          },
          {
            "Sequence": 2,
            "Name": "PushToMerchantFinalAmount",
            "TransactionType": "AchPush",
            "ConditionType": "Wait",
            "Condition": "CollectToFunding",
            "DelayDaysType": "None",
            "TransactionAmount": [
              "FinalAmount"
              //"-MerchantFee",
              //"-CustomerFees" // TBC
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "DynamicSpvFunding", // from
            "RetryPolicy": "None"
          }
        ],
        "RetryPolicy": {
          "NotEnoughBalanceMaxPeriodInDays": 5,
          "TooManyRequestsMaxAttemptsCount": 5
        }
      },
      {
        "FlowTemplateCode": "CREATE.FACTORING.FINAL_PAYMENT",
        "TemplateName": "Factoring ACH Final Payment Flow",
        "PaymentSubscription": null,
        "Product": "PayNow",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "CollectToFunding",
            "TransactionType": "AchInternal",
            "ConditionType": "None",
            "Condition": null,
            "DelayDaysType": "None",
            "TransactionAmount": [
              "FinalAmount"
              //"-MerchantFee",
              //"-CustomerFees" // TBC
            ],
            "OriginatorAccount": "DynamicSpvFunding", // to
            "ReceiverAccount": "DynamicSpvCollection", // from
            "RetryPolicy": "Exponential4Times"
          },
          {
            "Sequence": 2,
            "Name": "PushToMerchantFinalAmount",
            "TransactionType": "AchPush",
            "ConditionType": "Wait",
            "Condition": "CollectToFunding",
            "DelayDaysType": "None",
            "TransactionAmount": [
              "FinalAmount"
              //"-MerchantFee",
              //"-CustomerFees" // TBC
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "DynamicSpvFunding", // from
            "RetryPolicy": "None"
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.PAYNOW.DISBURSEMENT.V2",
        "TemplateName": "PayNow ACH Disbursement Flow v2",
        "Product": "PayNow",
        "PaymentSubscription": "SUBSCRIPTION1",
        "Version": "2.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "PushToMerchantFullAmount",
            "TransactionType": "AchPush",
            "ConditionType": "None",
            "DelayDaysType": "MerchantAchDelay",
            "Condition": null,
            "TransactionAmount": [
              "AdvanceAmount"
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "FUNDING", // from
            "RetryPolicy": "None"
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.PAYNOW.INVOICE_PAYMENT.V2",
        "TemplateName": "PayNow ACH Flow v2",
        "Product": "PayNow",
        "PaymentSubscription": null,
        "Version": "2.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "PullFromCustomer",
            "TransactionType": "AchPull",
            "ConditionType": "None",
            "DelayDaysType": "None",
            "Condition": null,
            "TransactionAmount": [
              "RepaymentAmount"
            ],
            "OriginatorAccount": "DynamicSpvCollection", // to
            "ReceiverAccount": "BORROWER" // from
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.FACTORING.DISBURSEMENT",
        "TemplateName": "Factoring ACH Disbursement Flow",
        "Product": "PayNow",
        "PaymentSubscription": null,
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "PushToMerchantAdvanceAmount",
            "TransactionType": "AchPush",
            "ConditionType": "Wait",
            "Condition": "WaitForEligibleBalanceFIFO",
            "DelayDaysType": "None",
            "TransactionAmount": [
              "AdvanceAmount"
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "DynamicSpvFunding" // from
          }
        ],
        "RetryPolicy": {
          "NotEnoughBalanceMaxPeriodInDays": 5,
          "TooManyRequestsMaxAttemptsCount": 5
        }
      },
      {
        "FlowTemplateCode": "CREATE.DRAW.DISBURSEMENT",
        "TemplateName": "Draw Disbursement Flow",
        "Product": "TradeCredit",
        "PaymentSubscription": null,
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "PushToMerchantAdvanceAmount",
            "TransactionType": "AchPush",
            "ConditionType": "Wait",
            "Condition": "WaitForEligibleBalanceFIFO", //CollectAdvancePaymentToFBO
            "DelayDaysType": "None",
            "TransactionAmount": [
              "AdvancePaymentAmount"
            ],
            "OriginatorAccount": "MERCHANT", // to
            "ReceiverAccount": "DynamicSpvFunding", // from
            "RetryPolicy": "Exponential3Times"
          }
        ]
      },
      {
        "FlowTemplateCode": "CREATE.IHC.REPAYMENT",
        "TemplateName": "Draw Repayment Flow",
        "PaymentSubscription": null,
        "Product": "InHouseCredit",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "CollectFromBorrower",
            "TransactionType": "AchPull",
            "ConditionType": "None",
            "DelayDaysType": "AchHold",
            "Condition": null,
            "TransactionAmount": [
              "RepaymentAmount"
            ],
            "OriginatorAccount": "DynamicSpvCollection", // to
            "ReceiverAccount": "BORROWER" // from
          }
        ],
        "RetryPolicy": {
          "NotEnoughBalanceMaxPeriodInDays": 5,
          "TooManyRequestsMaxAttemptsCount": 5
        }
      },
      {
        "FlowTemplateCode": "CREATE.SUBSCRIPTIONFEE.PAYMENT",
        "TemplateName": "Pull Supplier Subscription Fee Flow",
        "Product": "",
        "PaymentSubscription": "SUBSCRIPTION1",
        "Version": "1.0",
        "Steps": [
          {
            "Sequence": 1,
            "Name": "CollectFromMerchant",
            "TransactionType": "AchPull",
            "ConditionType": "None",
            "DelayDaysType": "AchHold",
            "Condition": null,
            "TransactionAmount": [
              "RequestedAmount"
            ],
            "OriginatorAccount": "REVENUE",
            "ReceiverAccount": "BORROWER",
            "RetryPolicy": "none"
          }
        ]
      }
    ]
  },
  "CustomOrderingConfig": {
    "IsCustomOrderingEnabled": false,
    "SupportedPaymentTypes": [
      "FactoringDisbursement",
      "FactoringFinalPayment",
      "DrawDisbursement",
      "FinalPayment",
      "FinalPaymentV2"
    ],
    "SupportedPaymentMethods": [
      "Instant",
      "Wire",
      "SameDayAch",
      "Ach"
    ],
    "AutoAssignSequenceNumbers": true,
    "MaxReorderBatchSize": 100
  },
  "PaymentWindowConfig": {
    "IsPaymentWindowEnabled": true,
    "AllowedPaymentSubscriptions": [
      "SUBSCRIPTION1",
      "SUBSCRIPTION2",
      "SUBSCRIPTION3"
    ],
    "AffectedPaymentTypes": [
      "FactoringDisbursement",
      "FactoringFinalPayment",
      "DrawDisbursement",
      "FinalPayment",
      "FinalPaymentV2"
    ],
    "DefaultConfig": {
      "AllowedPaymentMethods": [
        "Wire",
        "Instant"
      ],
      "PaymentMethodPriorities": {
        "Ach": 5,
        "SameDayAch": 6,
        "Wire": 3,
        "Instant": 3,
        "Card": 7
      }
    },
    "PaymentWindows": [
      {
        "Id": "priority-ach",
        "Name": "Same Day ACH Window",
        "StartTime": "09:23:00",
        "DurationMinutes": 10,
        "Priority": 1,
        "IsEnabled": true,
        "ActiveDays": [ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday" ],
        "AllowedPaymentMethods": [ "SameDayAch" ],
        "AllowedPaymentTypes": [
          "FactoringDisbursement",
          "DrawDisbursement",
          "FinalPayment"
        ]
      },
      {
        "Id": "priority-ach-prefunded",
        "Name": "Same Day ACH Window",
        "StartTime": "09:33:00",
        "DurationMinutes": 30,
        "Priority": 2,
        "IsEnabled": true,
        "ActiveDays": [ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday" ],
        "AllowedPaymentMethods": [ "SameDayAch" ],
        "AllowedPaymentTypes": [
          "FactoringFinalPayment",
          "FinalPaymentV2"
        ]
      },
      {
        "Id": "standard-ach",
        "Name": "Standard ACH Window",
        "StartTime": "15:23:00",
        "DurationMinutes": 10,
        "Priority": 1,
        "IsEnabled": true,
        "ActiveDays": [ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday" ],
        "AllowedPaymentMethods": [ "Ach" ],
        "AllowedPaymentTypes": [
          "FactoringDisbursement",
          "DrawDisbursement",
          "FinalPayment"
        ]
      },
      {
        "Id": "standard-ach-prefunded",
        "Name": "Standard ACH Window",
        "StartTime": "15:33:00",
        "DurationMinutes": 30,
        "Priority": 2,
        "IsEnabled": true,
        "ActiveDays": [ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday" ],
        "AllowedPaymentMethods": [ "Ach" ],
        "AllowedPaymentTypes": [
          "FactoringFinalPayment",
          "FinalPaymentV2"
        ]
      }
    ]
  }
}
