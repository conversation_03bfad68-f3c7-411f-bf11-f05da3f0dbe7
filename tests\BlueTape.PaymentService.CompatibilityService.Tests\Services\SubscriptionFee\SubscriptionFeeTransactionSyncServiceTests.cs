using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Services.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Tests.Entities;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Xunit;

namespace BlueTape.PaymentService.CompatibilityService.Tests.Services.SubscriptionFee;

public class SubscriptionFeeTransactionSyncServiceTests
{
    private readonly SubscriptionFeeTransactionSyncService _service;
    private readonly ISubscriptionFeeCompatibilityMapper _compatibilityMapper = Substitute.For<ISubscriptionFeeCompatibilityMapper>();
    private readonly ITransactionsRepository _transactionsRepository = Substitute.For<ITransactionsRepository>();
    private readonly ILogger<SubscriptionFeeTransactionSyncService> _logger = Substitute.For<ILogger<SubscriptionFeeTransactionSyncService>>();

    public SubscriptionFeeTransactionSyncServiceTests()
    {
        _service = new SubscriptionFeeTransactionSyncService(
            _compatibilityMapper,
            _transactionsRepository,
            _logger);
    }

    [Fact]
    public async Task PerformTransactions_ValidInputs_CreatesTransaction()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var operation = OperationEntities.OperationsToCreate.First();
        var legacyTransaction = TransactionEntities.TransactionsToCreateOperation.First();
        var cancellationToken = CancellationToken.None;

        _compatibilityMapper.MapFromPaymentTransactionToSubscriptionFeeLegacyTransaction(paymentRequest, operation, cancellationToken)
            .Returns(legacyTransaction);

        // Act
        await _service.PerformTransactions(paymentRequest, operation, cancellationToken);

        // Assert
        await _transactionsRepository.Received(1).InsertMany(
            Arg.Is<List<TransactionEntity>>(transactions => transactions.Count == 1 && transactions.First() == legacyTransaction),
            cancellationToken);
    }

    [Fact]
    public async Task SyncTransactions_NoOperationIds_LogsWarningAndReturns()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var operationIds = new List<string>();
        var cancellationToken = CancellationToken.None;

        // Act
        await _service.SyncTransactions(paymentRequest, operationIds, cancellationToken);

        // Assert
        await _transactionsRepository.DidNotReceive().GetByOperationIds(Arg.Any<List<string>>(), Arg.Any<string>(), cancellationToken);
    }

    [Fact]
    public async Task PerformTransactions_CompatibilityMapperThrows_RethrowsException()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var operation = OperationEntities.OperationsToCreate.First();
        var cancellationToken = CancellationToken.None;
        var expectedException = new InvalidOperationException("Mapping failed");

        _compatibilityMapper.MapFromPaymentTransactionToSubscriptionFeeLegacyTransaction(paymentRequest, operation, cancellationToken)
            .Returns<TransactionEntity>(_ => throw expectedException);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            () => _service.PerformTransactions(paymentRequest, operation, cancellationToken));

        exception.Message.ShouldBe("Mapping failed");
    }

    [Fact]
    public async Task SyncTransactions_NoExistingTransactions_CompletesWithoutUpdates()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var operationIds = new List<string> { "operation1" };
        var cancellationToken = CancellationToken.None;

        // Setup payment request with ACH pull transactions
        paymentRequest.Transactions.First().TransactionType = PaymentTransactionType.AchPull;

        _transactionsRepository.GetByOperationIds(operationIds, LegacyTransactionType.PULL.ToString(), cancellationToken)
            .Returns(new List<TransactionEntity>());

        // Act
        await _service.SyncTransactions(paymentRequest, operationIds, cancellationToken);

        // Assert
        await _transactionsRepository.DidNotReceive().UpdateById(Arg.Any<string>(), Arg.Any<UpdateTransactionEntity>(), cancellationToken);
    }
}
