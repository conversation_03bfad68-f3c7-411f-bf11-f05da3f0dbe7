using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Models;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.PaymentService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Tags("Payment Window Configuration")]
public class PaymentWindowController(IPaymentWindowService paymentWindowService) : ControllerBase
{
    [HttpGet("config")]
    public async Task<ActionResult<PaymentWindowConfig>> GetConfig(CancellationToken ct)
    {
        var config = await paymentWindowService.GetPaymentWindowConfig(ct);
        return Ok(config);
    }

    [HttpPut("config")]
    public async Task<ActionResult> UpdateConfig([FromBody] PaymentWindowConfig config, CancellationToken ct)
    {
        if (config == null)
            return BadRequest("Configuration cannot be null");

        await paymentWindowService.UpdatePaymentWindowConfig(config, "API", ct);

        return Ok(new
        {
            Message = "Payment window configuration updated successfully",
            Config = config
        });
    }

    [HttpGet("status")]
    public async Task<ActionResult> GetStatus(CancellationToken ct)
    {
        var isActive = await paymentWindowService.IsPaymentWindowActive(ct);
        var nextWindowStart = await paymentWindowService.GetNextPaymentWindowStartTime(ct);
        var currentWindowEnd = await paymentWindowService.GetCurrentPaymentWindowEndTime(ct);

        return Ok(new
        {
            IsPaymentWindowActive = isActive,
            CurrentWindowEndTime = currentWindowEnd,
            NextWindowStartTime = nextWindowStart,
            Timestamp = DateTime.UtcNow
        });
    }

    [HttpGet("next-window")]
    public async Task<ActionResult> GetNextWindow(CancellationToken ct)
    {
        var nextWindowStart = await paymentWindowService.GetNextPaymentWindowStartTime(ct);

        if (nextWindowStart == null)
        {
            return Ok(new
            {
                Message = "No upcoming payment windows configured",
                NextWindowStartTime = (DateTime?)null
            });
        }

        var config = await paymentWindowService.GetPaymentWindowConfig(ct);

        return Ok(new
        {
            NextWindowStartTime = nextWindowStart,
        });
    }

    [HttpPost("toggle")]
    public async Task<ActionResult> TogglePaymentWindow([FromQuery] bool enabled, CancellationToken ct)
    {
        var config = await paymentWindowService.GetPaymentWindowConfig(ct);
        config.IsPaymentWindowEnabled = enabled;

        await paymentWindowService.UpdatePaymentWindowConfig(config, "API", ct);

        return Ok(new
        {
            Message = $"Payment window {(enabled ? "enabled" : "disabled")} successfully",
            IsEnabled = enabled
        });
    }

    [HttpPut("duration/{durationMinutes}")]
    public async Task<ActionResult> UpdateDuration(int durationMinutes, CancellationToken ct)
    {
        if (durationMinutes <= 0)
            return BadRequest("Duration must be greater than 0");

        if (durationMinutes > 1440)
            return BadRequest("Duration cannot exceed 1440 minutes (24 hours)");

        var config = await paymentWindowService.GetPaymentWindowConfig(ct);

        await paymentWindowService.UpdatePaymentWindowConfig(config, "API", ct);

        return Ok(new
        {
            Message = $"Payment window duration updated to {durationMinutes} minutes",
            DurationMinutes = durationMinutes
        });
    }
}
