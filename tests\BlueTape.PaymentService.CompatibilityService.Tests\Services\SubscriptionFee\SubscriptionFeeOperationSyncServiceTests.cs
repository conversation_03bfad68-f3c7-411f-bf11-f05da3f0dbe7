using AutoMapper;
using BlueTape.InvoiceService.Messages;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Senders;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Services.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Tests.Entities;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.ServiceBusMessaging.Attributes;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Xunit;

namespace BlueTape.PaymentService.CompatibilityService.Tests.Services.SubscriptionFee;

public class SubscriptionFeeOperationSyncServiceTests
{
    private readonly SubscriptionFeeOperationSyncService _service;
    private readonly IOperationsRepository _operationsRepository = Substitute.For<IOperationsRepository>();
    private readonly ISubscriptionFeeCompatibilityMapper _compatibilityMapper = Substitute.For<ISubscriptionFeeCompatibilityMapper>();
    private readonly ISubscriptionFeeTransactionSyncService _transactionSyncService = Substitute.For<ISubscriptionFeeTransactionSyncService>();
    private readonly IMapper _mapper = Substitute.For<IMapper>();
    private readonly IInvoiceSyncMessageSender _messageSender = Substitute.For<IInvoiceSyncMessageSender>();
    private readonly ILogger<SubscriptionFeeOperationSyncService> _logger = Substitute.For<ILogger<SubscriptionFeeOperationSyncService>>();

    public SubscriptionFeeOperationSyncServiceTests()
    {
        _service = new SubscriptionFeeOperationSyncService(
            _operationsRepository,
            _compatibilityMapper,
            _transactionSyncService,
            _mapper,
            _messageSender,
            _logger);
    }

    [Fact]
    public async Task PerformOperation_ValidPaymentRequest_CreatesOperationAndTransactions()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var operation = OperationEntities.OperationsToCreate.First();
        var cancellationToken = CancellationToken.None;

        _compatibilityMapper.MapFromPaymentRequestToSubscriptionFeeOperation(paymentRequest)
            .Returns(operation);

        // Act
        await _service.PerformOperation(paymentRequest, cancellationToken);

        // Assert
        await _operationsRepository.Received(1).InsertMany(
            Arg.Is<List<OperationEntity>>(ops => ops.Count == 1 && ops.First() == operation),
            cancellationToken);

        await _transactionSyncService.Received(1).PerformTransactions(paymentRequest, operation, cancellationToken);
    }

    [Fact]
    public async Task SyncOperation_PaymentRequestSettled_SendsInvoiceSyncMessage()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        paymentRequest.Status = PaymentRequestStatus.Settled;
        var existingOperations = OperationEntities.OperationsToCreate;
        var cancellationToken = CancellationToken.None;

        _operationsRepository.GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
            .Returns(existingOperations);

        // Act
        await _service.SyncOperation(paymentRequest, cancellationToken);

        // Assert
        await _messageSender.Received(1).SendMessages(
            Arg.Any<IEnumerable<ServiceBusMessageBt<SyncInvoiceMessagePayload>>>(), cancellationToken);
    }

    [Fact]
    public async Task SyncOperation_OperationWithFailStatus_SkipsUpdate()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var failedOperation = OperationEntities.OperationsToCreate.First();
        failedOperation.Status = OperationStatus.FAIL.ToString();
        var existingOperations = new List<OperationEntity> { failedOperation };
        var cancellationToken = CancellationToken.None;

        _operationsRepository.GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
            .Returns(existingOperations);

        // Act
        await _service.SyncOperation(paymentRequest, cancellationToken);

        // Assert
        await _operationsRepository.DidNotReceive().UpdateById(failedOperation.BlueTapeId, Arg.Any<UpdateOperationEntity>(), cancellationToken);
    }

    [Fact]
    public async Task PerformOperation_CompatibilityMapperThrows_RethrowsException()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var cancellationToken = CancellationToken.None;
        var expectedException = new InvalidOperationException("Mapping failed");

        _compatibilityMapper.MapFromPaymentRequestToSubscriptionFeeOperation(paymentRequest)
            .Returns<OperationEntity>(_ => throw expectedException);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            () => _service.PerformOperation(paymentRequest, cancellationToken));

        exception.Message.ShouldBe("Mapping failed");
    }

    [Fact]
    public async Task SyncOperation_NoExistingOperations_CompletesWithoutError()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;
        var cancellationToken = CancellationToken.None;

        _operationsRepository.GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
            .Returns(new List<OperationEntity>());

        // Act
        await _service.SyncOperation(paymentRequest, cancellationToken);

        // Assert
        await _operationsRepository.DidNotReceive().UpdateById(Arg.Any<string>(), Arg.Any<UpdateOperationEntity>(), cancellationToken);
        await _transactionSyncService.Received(1).SyncTransactions(paymentRequest, Arg.Is<List<string>>(ids => !ids.Any()), cancellationToken);
    }
}
