﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IFinalPaymentRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(FinalPaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
