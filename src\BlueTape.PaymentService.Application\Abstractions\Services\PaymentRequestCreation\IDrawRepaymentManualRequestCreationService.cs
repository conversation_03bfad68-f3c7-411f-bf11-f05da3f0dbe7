﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IDrawRepaymentManualRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(DrawRepaymentManualRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
