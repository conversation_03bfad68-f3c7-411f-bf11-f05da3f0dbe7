using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace BlueTape.PaymentService.Application.Services;

public class PaymentWindowService(
    IPaymentConfigRepository paymentConfigRepository,
    IConfiguration configuration,
    IDateProvider dateProvider,
    ILogger<PaymentWindowService> logger)
    : IPaymentWindowService
{
    private const string PaymentWindowConfigKey = "PAYMENT_WINDOW_CONFIG";

    public async Task<bool> IsPaymentWindowActive(CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);

        if (!config.IsPaymentWindowEnabled)
            return false;

        var currentTime = dateProvider.CurrentDateTime;

        return IsAnyPaymentWindowActive(config, currentTime);
    }

    public List<PaymentWindow> GetActivePaymentWindows(PaymentWindowConfig config)
    {
        if (!config.IsPaymentWindowEnabled)
            return new List<PaymentWindow>();

        var currentTime = dateProvider.CurrentDateTime;
        return GetActivePaymentWindows(config, currentTime);
    }

    private bool IsAnyPaymentWindowActive(PaymentWindowConfig config, DateTime currentTime)
    {
        return GetActivePaymentWindows(config, currentTime).Any();
    }

    private List<PaymentWindow> GetActivePaymentWindows(PaymentWindowConfig config, DateTime currentTime)
    {
        var activeWindows = new List<PaymentWindow>();
        var currentDay = currentTime.DayOfWeek;

        foreach (var window in config.PaymentWindows.Where(w => w.IsEnabled))
        {
            if (!window.ActiveDays.Contains(currentDay))
                continue;

            var windowStart = currentTime.Date.Add(window.StartTime);
            var windowEnd = windowStart.AddMinutes(window.DurationMinutes);

            if (currentTime >= windowStart && currentTime <= windowEnd)
            {
                activeWindows.Add(window);

                logger.LogDebug(
                    "Payment window '{WindowName}' is active: Current={CurrentTime}, WindowStart={WindowStart}, WindowEnd={WindowEnd}",
                    window.Name, currentTime, windowStart, windowEnd);
            }
        }

        return activeWindows;
    }

    public async Task<PaymentWindowConfig> GetPaymentWindowConfig(CancellationToken ct)
    {
        return LoadConfigFromDatabaseOrDefault(ct).GetAwaiter().GetResult();
    }

    private async Task<PaymentWindowConfig> LoadConfigFromDatabaseOrDefault(CancellationToken ct)
    {
        var configEntity = await paymentConfigRepository.GetByConfigKey(PaymentWindowConfigKey, ct);

        if (configEntity == null)
        {
            logger.LogInformation("Payment window config not found in database, loading from appsettings and initializing database");

            // Load from appsettings
            var defaultConfig = LoadConfigFromAppSettings();

            // Save to database for future use
            await SaveConfigToDatabase(defaultConfig, "System", ct);

            return defaultConfig;
        }

        try
        {
            var config = JsonSerializer.Deserialize<PaymentWindowConfig>(configEntity.ConfigValue);
            logger.LogDebug("Payment window configuration loaded from database");
            return config ?? LoadConfigFromAppSettings();
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to deserialize payment window configuration from database, falling back to appsettings");
            return LoadConfigFromAppSettings();
        }
    }

    private PaymentWindowConfig LoadConfigFromAppSettings()
    {
        try
        {
            var configSection = configuration.GetSection("PaymentWindowConfig");
            var config = configSection.Get<PaymentWindowConfig>();

            if (config == null)
            {
                logger.LogWarning("PaymentWindowConfig section not found in appsettings, using hardcoded defaults");
                return new PaymentWindowConfig();
            }

            logger.LogDebug("Payment window configuration loaded from appsettings");
            return config;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to load payment window configuration from appsettings, using hardcoded defaults");
            return new PaymentWindowConfig();
        }
    }

    private async Task SaveConfigToDatabase(PaymentWindowConfig config, string createdBy, CancellationToken ct)
    {
        try
        {
            config.UpdatedAt = dateProvider.CurrentDateTime;
            config.UpdatedBy = createdBy;

            var configJson = JsonSerializer.Serialize(config);
            var configEntity = new PaymentConfigEntity
            {
                ConfigKey = PaymentWindowConfigKey,
                ConfigValue = configJson,
                Description = "Payment window configuration for ACH/Same Day ACH processing",
                CreatedAt = dateProvider.CurrentDateTime,
                CreatedBy = createdBy,
                UpdatedAt = dateProvider.CurrentDateTime,
                UpdatedBy = createdBy
            };

            await paymentConfigRepository.Add(configEntity, ct);
            logger.LogInformation("Payment window configuration saved to database by {CreatedBy}", createdBy);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save payment window configuration to database");
        }
    }

    public async Task UpdatePaymentWindowConfig(PaymentWindowConfig config, string updatedBy, CancellationToken ct)
    {
        config.UpdatedAt = dateProvider.CurrentDateTime;
        config.UpdatedBy = updatedBy;

        var configJson = JsonSerializer.Serialize(config);
        var configEntity = await paymentConfigRepository.GetByConfigKey(PaymentWindowConfigKey, ct);

        if (configEntity == null)
        {
            configEntity = new PaymentConfigEntity
            {
                ConfigKey = PaymentWindowConfigKey,
                ConfigValue = configJson,
                Description = "Payment window configuration for ACH/Same Day ACH processing",
                CreatedAt = dateProvider.CurrentDateTime,
                CreatedBy = updatedBy
            };
            await paymentConfigRepository.Add(configEntity, ct);
        }
        else
        {
            configEntity.ConfigValue = configJson;
            configEntity.UpdatedAt = dateProvider.CurrentDateTime;
            configEntity.UpdatedBy = updatedBy;
            await paymentConfigRepository.Update(configEntity, ct);
        }

        logger.LogInformation("Payment window configuration updated by {UpdatedBy}", updatedBy);
    }

    public bool ShouldProcessPaymentMethod(PaymentWindowConfig config, PaymentRequestCommandEntity command, CancellationToken ct)
    {
        var paymentType = command.PaymentRequest.RequestType;
        var paymentMethod = command.PaymentRequest.PaymentMethod;

        // If payment not affected by payment windows or is a prefunded payment in processing status
        if (!config.AffectedPaymentTypes.Contains(paymentType) || ((IsPrefundedFinalPayment(command) && command.PaymentRequest.Status == PaymentRequestStatus.Processing)))
        {
            return true;
        }

        var activeWindows = GetActivePaymentWindows(config);

        if (!config.IsPaymentWindowEnabled)
        {
            return config.DefaultConfig.AllowedPaymentMethods.Contains(paymentMethod);
        }

        // If any windows active - filter by method and type
        if (activeWindows.Any())
        {
            return activeWindows.Any(window => window.AllowedPaymentMethods.Contains(paymentMethod) && window.AllowedPaymentTypes.Contains(paymentType));
        }

        return config.DefaultConfig.AllowedPaymentMethods.Contains(paymentMethod);
    }

    public bool IsPrefundedFinalPayment(PaymentRequestCommandEntity command)
    {
        if (command.PaymentRequest == null)
            return false;

        return command.PaymentRequest.RequestType == PaymentRequestType.FinalPaymentV2 ||
               command.PaymentRequest.RequestType == PaymentRequestType.FactoringFinalPayment;
    }

    public async Task<List<PaymentRequestCommandEntity>> FilterCommandsByPaymentWindow(List<PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);
        var activeWindows = GetActivePaymentWindows(config);

        if (!config.IsPaymentWindowEnabled)
        {
            // If payment windows are disabled - return all commands
            logger.LogInformation("Payment windows are disabled or no active windows, returning all commands");
            return commands;
        }

        var filteredCommands = new List<PaymentRequestCommandEntity>();

        foreach (var command in commands)
        {
            if (ShouldProcessPaymentMethod(config, command, ct))
            {
                filteredCommands.Add(command);
            }
        }

        // Filtered out payment requests that has been approved after the start of the active payment window but allow to process prefunded reqeusts
        if (activeWindows.Any())
        {
            var windowStartDate = dateProvider.CurrentDate.ToDateTime(TimeOnly.MinValue) + activeWindows.FirstOrDefault().StartTime;
            filteredCommands =
                filteredCommands.Where(x => !config.AffectedPaymentTypes.Contains(x.PaymentRequest.RequestType)
                                            || (IsPrefundedFinalPayment(x) && x.PaymentRequest.Status == PaymentRequestStatus.Processing)
                                            || x.PaymentRequest.ConfirmedAt < windowStartDate).ToList();
        }

        logger.LogInformation(
            "Filtered commands by payment window: {FilteredCount} out of {TotalCount} commands will be processed",
            filteredCommands.Count, commands.Count);

        return filteredCommands;
    }

    public async Task<DateTime?> GetNextPaymentWindowStartTime(CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);

        if (!config.IsPaymentWindowEnabled || !config.PaymentWindows.Any(w => w.IsEnabled))
            return null;

        var currentTime = dateProvider.CurrentDateTime;
        var searchDate = currentTime.Date;

        // Look for the next 7 days to find an active day for any window
        for (int i = 0; i < 7; i++)
        {
            var checkDate = searchDate.AddDays(i);
            var dayOfWeek = checkDate.DayOfWeek;

            // Find the earliest window start time on this day
            var earliestWindow = config.PaymentWindows
                .Where(w => w.IsEnabled && w.ActiveDays.Contains(dayOfWeek))
                .OrderBy(w => w.StartTime)
                .FirstOrDefault();

            if (earliestWindow != null)
            {
                var windowStartTime = checkDate.Add(earliestWindow.StartTime);

                // If it's today, make sure the window hasn't passed
                if (i == 0 && currentTime >= windowStartTime.AddMinutes(earliestWindow.DurationMinutes))
                    continue;

                return windowStartTime;
            }
        }

        return null;
    }

    public async Task<DateTime?> GetCurrentPaymentWindowEndTime(CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);
        var activeWindows = GetActivePaymentWindows(config);
        if (!activeWindows.Any())
            return null;

        var currentTime = dateProvider.CurrentDateTime;

        // Return the latest end time among all active windows
        var latestEndTime = activeWindows.Max(window =>
        {
            var windowStart = currentTime.Date.Add(window.StartTime);
            return windowStart.AddMinutes(window.DurationMinutes);
        });

        return latestEndTime;
    }

    public async Task<string> GetCurrentProcessingMode(CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);

        if (!config.IsPaymentWindowEnabled)
            return "Default Processing (Windows Disabled)";

        var activeWindows = GetActivePaymentWindows(config);

        if (!activeWindows.Any())
            return "Default Processing";

        var primaryWindow = activeWindows.OrderBy(w => w.Priority).First();
        return $"Window Processing: {primaryWindow.Name}";
    }
}
