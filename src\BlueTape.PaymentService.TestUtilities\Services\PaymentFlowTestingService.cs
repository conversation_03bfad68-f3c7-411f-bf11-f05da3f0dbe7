﻿using BlueTape.Integrations.Aion.Infrastructure.Constants;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.TestUtilities.Abstractions.Services;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Shouldly;

namespace BlueTape.PaymentService.TestUtilities.Services;
public class PaymentFlowTestingService(
    IPaymentRequestService paymentRequestService,
    ICommandManagementService commandManagementService,
    ITransactionStatusUpdateService transactionStatusUpdateService,
    IUnitOfWork unitOfWork) : IPaymentFlowTestService
{
    public async Task ExecutePaymentRequest(Guid paymentRequestId, CancellationToken ct)
    {
        var paymentRequest = await unitOfWork.PaymentRequestRepository.GetById(paymentRequestId, ct, includeProperties: "PaymentRequestDetails,Transactions,PaymentRequestCommands");
        var transactions = paymentRequest!.Transactions
            .OrderBy(x => x.SequenceNumber)
            .ToList();

        if (paymentRequest!.ConfirmationType == ConfirmationType.Manual && paymentRequest.ConfirmedAt is null)
        {
            await paymentRequestService.ApprovePaymentRequest(paymentRequest.Id, "testUtilities", new PaymentApprovalRequest(), ct);
        }

        if (paymentRequest!.MerchantAchDelayInBusinessDays > 0)
        {
            paymentRequest.MerchantAchDelayInBusinessDays = 0;
            await unitOfWork.PaymentRequestRepository.Update(paymentRequest, ct);
        }

        if (paymentRequest!.Status == PaymentRequestStatus.Requested)
        {
            var firstCommand = paymentRequest.PaymentRequestCommands.First(x => x.TransactionId == transactions.First(x => x.SequenceNumber == 1).Id);
            await commandManagementService.ManageCommand(firstCommand.Id, ct);
            paymentRequest = await unitOfWork.PaymentRequestRepository.GetById(paymentRequestId, ct, includeProperties: "PaymentRequestDetails,Transactions,PaymentRequestCommands");
            transactions = paymentRequest!.Transactions
                .OrderBy(x => x.SequenceNumber)
                .ThenBy(x => x.CreatedAt)
                .ToList();
        }

        foreach (var transaction in transactions)
        {
            var actualTransaction = await unitOfWork.PaymentTransactionRepository.GetById(transaction.Id, ct);
            if (actualTransaction!.Status is TransactionStatus.Cleared)
                continue;

            var command = await unitOfWork.PaymentRequestCommandRepository
                .GetById(paymentRequest.PaymentRequestCommands.First(x => x.TransactionId == actualTransaction.Id).Id, ct);

            if (command!.Status == CommandStatus.Placed)
            {
                command.Status = CommandStatus.Pending;
                await unitOfWork.PaymentRequestCommandRepository.Update(command, ct);
                command = await unitOfWork.PaymentRequestCommandRepository.GetById(command.Id, ct);
            }

            if (command.Status == CommandStatus.Pending)
            {
                try
                {
                    await commandManagementService.ManageCommand(command.Id, ct);
                }
                catch (Exception ex) { }
            }

            var referenceNumber = string.IsNullOrEmpty(actualTransaction.ReferenceNumber) ? "REF-" + Guid.NewGuid().ToString("N")[..9].ToUpper() : transaction.ReferenceNumber;

            await Task.Delay(1000, ct).WaitAsync(ct);

            await transactionStatusUpdateService.Process(new TransactionStatusMessagePayload()
            {
                BlueTapeTransactionNumber = actualTransaction.TransactionNumber!,
                ExternalTransactionNumber = referenceNumber,
                ExternalTransactionStatus = AionStatuses.Cleared,
            }, "testUtilities", ct);

            await Task.Delay(1000, ct).WaitAsync(ct);
        }

        paymentRequest!.Status.ShouldBe(PaymentRequestStatus.Settled);
    }
}
