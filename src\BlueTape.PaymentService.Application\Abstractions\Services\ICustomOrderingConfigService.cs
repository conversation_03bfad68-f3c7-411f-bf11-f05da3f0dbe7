using BlueTape.PaymentService.Domain.Models.Configuration;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

/// <summary>
/// Service for managing custom ordering configuration
/// </summary>
public interface ICustomOrderingConfigService
{
    /// <summary>
    /// Gets the current custom ordering configuration
    /// </summary>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Custom ordering configuration</returns>
    Task<CustomOrderingConfig> GetCustomOrderingConfig(CancellationToken ct);

    /// <summary>
    /// Updates the custom ordering configuration
    /// </summary>
    /// <param name="config">New configuration</param>
    /// <param name="updatedBy">User who is updating the configuration</param>
    /// <param name="ct">Cancellation token</param>
    Task UpdateCustomOrderingConfig(CustomOrderingConfig config, string updatedBy, CancellationToken ct);

    /// <summary>
    /// Checks if custom ordering is enabled for the given payment request type and method
    /// </summary>
    /// <param name="paymentRequestType">Payment request type</param>
    /// <param name="paymentMethod">Payment method</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>True if custom ordering is supported</returns>
    Task<bool> IsCustomOrderingSupported(Domain.Enums.PaymentRequestType paymentRequestType, Domain.Enums.PaymentMethod paymentMethod, CancellationToken ct);

    /// <summary>
    /// Toggles custom ordering functionality on/off
    /// </summary>
    /// <param name="enabled">Whether to enable or disable</param>
    /// <param name="updatedBy">User making the change</param>
    /// <param name="ct">Cancellation token</param>
    Task ToggleCustomOrdering(bool enabled, string updatedBy, CancellationToken ct);
}
