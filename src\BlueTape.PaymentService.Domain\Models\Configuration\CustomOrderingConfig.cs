using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Domain.Models.Configuration;

/// <summary>
/// Configuration for custom ordering functionality in payment processing
/// </summary>
public class CustomOrderingConfig
{
    /// <summary>
    /// Whether custom ordering functionality is enabled
    /// </summary>
    public bool IsCustomOrderingEnabled { get; set; } = false;

    /// <summary>
    /// Payment request types that support custom ordering
    /// </summary>
    public List<PaymentRequestType> SupportedPaymentTypes { get; set; } = new()
    {
        PaymentRequestType.FactoringDisbursement,
        PaymentRequestType.FactoringFinalPayment,
        PaymentRequestType.DrawDisbursement,
        PaymentRequestType.FinalPayment,
        PaymentRequestType.FinalPaymentV2
    };

    /// <summary>
    /// Payment methods that support custom ordering
    /// </summary>
    public List<PaymentMethod> SupportedPaymentMethods { get; set; } = new()
    {
        PaymentMethod.Instant,
        PaymentMethod.Wire,
        PaymentMethod.SameDayAch,
        PaymentMethod.Ach
    };

    /// <summary>
    /// Whether to automatically assign sequence numbers when payments are approved
    /// </summary>
    public bool AutoAssignSequenceNumbers { get; set; } = true;

    /// <summary>
    /// Maximum number of payments that can be reordered in a single operation
    /// </summary>
    public int MaxReorderBatchSize { get; set; } = 100;

    /// <summary>
    /// When the configuration was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Who last updated the configuration
    /// </summary>
    public string? UpdatedBy { get; set; }
}
