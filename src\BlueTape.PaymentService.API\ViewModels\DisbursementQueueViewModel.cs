using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.API.ViewModels;

/// <summary>
/// View model for disbursement queue with payment requests grouped by payment method
/// </summary>
public class DisbursementQueueViewModel
{
    /// <summary>
    /// Payment method for this queue (Instant, Wire, SameDayAch, Ach)
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }

    /// <summary>
    /// Display name for the queue
    /// </summary>
    public string QueueName { get; set; } = string.Empty;

    /// <summary>
    /// Payment requests in this queue with row numbers
    /// </summary>
    public List<DisbursementQueueItemViewModel> PaymentRequests { get; set; } = new();

    /// <summary>
    /// Total number of payment requests in this queue
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total amount for all payment requests in this queue
    /// </summary>
    public decimal TotalAmount { get; set; }
}

/// <summary>
/// Individual payment request item in a disbursement queue
/// </summary>
public class DisbursementQueueItemViewModel
{
    /// <summary>
    /// Payment request ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Row number for display (starts from 1, hides actual sequence number gaps)
    /// </summary>
    public int RowNumber { get; set; }

    /// <summary>
    /// Payment request amount
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Fee amount
    /// </summary>
    public decimal FeeAmount { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Payment request type
    /// </summary>
    public PaymentRequestType RequestType { get; set; }

    /// <summary>
    /// Payment method
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }

    /// <summary>
    /// Payer ID
    /// </summary>
    public string? PayerId { get; set; }

    /// <summary>
    /// Payee ID
    /// </summary>
    public string? PayeeId { get; set; }

    /// <summary>
    /// When the payment was confirmed/approved
    /// </summary>
    public DateTime? ConfirmedAt { get; set; }

    /// <summary>
    /// Who confirmed/approved the payment
    /// </summary>
    public string? ConfirmedBy { get; set; }

    /// <summary>
    /// When the payment was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public PaymentRequestStatus Status { get; set; }

    /// <summary>
    /// Actual sequence number (for debugging, not displayed to users)
    /// </summary>
    public int SequenceNumber { get; set; }
}
