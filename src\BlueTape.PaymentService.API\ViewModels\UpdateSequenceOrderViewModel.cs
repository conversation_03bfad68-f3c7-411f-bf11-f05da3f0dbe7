using System.ComponentModel.DataAnnotations;

namespace BlueTape.PaymentService.API.ViewModels;

/// <summary>
/// Request model for updating the sequence order of payment requests
/// </summary>
public class UpdateSequenceOrderViewModel
{
    /// <summary>
    /// List of payment request IDs in their desired order
    /// </summary>
    [Required]
    [MinLength(1, ErrorMessage = "At least one payment request ID is required")]
    [MaxLength(100, ErrorMessage = "Cannot reorder more than 100 payments at once")]
    public List<Guid> PaymentRequestIds { get; set; } = new();
}

/// <summary>
/// Response model for sequence order update operation
/// </summary>
public class UpdateSequenceOrderResponseViewModel
{
    /// <summary>
    /// Number of payment requests that were updated
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// Success message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// List of payment request IDs that were included in the update (including newly approved ones)
    /// </summary>
    public List<Guid> ProcessedPaymentRequestIds { get; set; } = new();
}
