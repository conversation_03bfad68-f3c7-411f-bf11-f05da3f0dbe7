﻿using BlueTape.LS.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Domain.Models.PaymentRequest;

public class CreatePaymentRequestModel
{
    public SubjectType SubjectType { get; set; }
    public PaymentRequestType RequestType { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public string FlowTemplateCode { get; set; } = null!;
    public string? PayerId { get; set; }
    public string? PayeeId { get; set; }
    public string? SellerId { get; set; }
    public string? CustomerAccountId { get; set; }
    public Guid? CreditId { get; set; }
    public string? ProjectId { get; set; }
    public Guid? DrawId { get; set; }
    public Guid? LmsPaymentId { get; set; }
    public decimal Amount { get; set; }
    public decimal FeeAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public DateOnly Date { get; set; }
    public int MerchantAchDelayInBusinessDays { get; set; }
    public DateTime? ExecuteAfter { get; set; }
    public string? ParentReferenceNumber { get; set; }
    public List<CreatePaymentRequestPayableModel> PaymentRequestPayables { get; set; } = new();
    public List<CreatePaymentRequestFeeModel> PaymentRequestFees { get; set; } = new();
    public List<CreatePaymentRequestDiscountModel> PaymentRequestDiscounts { get; set; } = new();
    public CreateManualPaymentDetails? ManualPaymentDetails { get; set; }
    public CreateAdditionalDetails? AdditionalDetails { get; set; }
    public ConfirmationType ConfirmationType { get; set; }
    public FundingSource? FundingSource { get; set; }
    public bool? IsAutoPayment { get; set; }

    /// <summary>
    /// Sequence number for custom ordering of disbursement payments.
    /// 0 means no custom ordering (uses default priority).
    /// Will be automatically assigned if not provided.
    /// </summary>
    public int SequenceNumber { get; set; } = 0;
}
