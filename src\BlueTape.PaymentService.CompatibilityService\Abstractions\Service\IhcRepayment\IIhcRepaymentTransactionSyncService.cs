using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Entities;

namespace BlueTape.PaymentService.CompatibilityService.Abstractions.Service.IhcRepayment;

public interface IIhcRepaymentTransactionSyncService
{
    Task PerformTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken cancellationToken);
    Task SyncTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken cancellationToken);
}
