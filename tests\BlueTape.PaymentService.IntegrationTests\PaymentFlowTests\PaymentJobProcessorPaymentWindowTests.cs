using BlueTape.Integrations.Aion.Infrastructure.Constants;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.PaymentService.IntegrationTests.PaymentFlowTests.Base;
using BlueTape.PaymentService.IntegrationTests.TestConstants;
using BlueTape.PaymentService.IntegrationTests.TestData;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;

namespace BlueTape.PaymentService.IntegrationTests.PaymentFlowTests;

[Collection(Configuration.SequentialExecution)]
public class PaymentJobProcessorPaymentWindowTests() : BasePaymentFlowTest(dbName: "processPaymentJob", mockDateProvider: true)
{
    [Fact]
    public async Task Process_FirstPaymentWindow_ExecuteOnlyNonPrefundedPayments()
    {
        // Arrange
        // Set current time to Tuesday 9:25 AM UTC (within the same-day ACH window: 9:23-9:33)
        var currentTime = DateTime.Today.AddDays(1) + new TimeSpan(9, 25, 0);
        _dateProviderMock.CurrentDateTime.Returns(currentTime);
        _dateProviderMock.CurrentDate.Returns(DateOnly.FromDateTime(currentTime));

        var processingInstantFinalPayment = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FactoringFinalPayment), paymentMethod: PaymentMethod.Instant);
        var commandToExecute = processingInstantFinalPayment.PaymentRequestCommands.First(x => x.SequenceNumber == 1);
        await ExecuteCommandManagement(commandToExecute.Id);
        var details = await GetPaymentRequestById(processingInstantFinalPayment!.Id);
        await ExecuteTransactionStatusUpdateConsumer(
            GetTransactionStatusMessage(details.Transactions.FirstOrDefault(x => x.Id == commandToExecute.TransactionId),
                AionStatuses.Cleared));

        // Set up payment window configuration for this test
        await SetupPaymentWindowConfigForTest(currentTime);

        // Payment that not affected by payment window
        var notAffectedByPayment = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.InvoicePayment));

        var drawAdvance = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.DrawDisbursement), paymentMethod: PaymentMethod.SameDayAch);
        var finalPayment = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FinalPayment), paymentMethod: PaymentMethod.SameDayAch);
        var finalPaymentV2 = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FinalPaymentV2), paymentMethod: PaymentMethod.SameDayAch);
        var factoringAdvance = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FactoringDisbursement), paymentMethod: PaymentMethod.SameDayAch);
        var factoringFinal = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FactoringFinalPayment), paymentMethod: PaymentMethod.SameDayAch);

        // Act
        await ExecutePaymentScheduledJob();

        // Assert
        var messages = _fakePaymentFlowServiceMessageSender.GetAll();

        // Verify that final payments are prioritized (should appear first in the message queue)
        var messageCommandIds = messages.Select(x => x.ExecutableCommandId).ToList();

        // Get payment window service to verify window is active
        var paymentWindowService = _serviceProvider.GetRequiredService<IPaymentWindowService>();
        var isWindowActive = await paymentWindowService.IsPaymentWindowActive(CancellationToken.None);

        // Assert that window is active and payments were processed
        Assert.True(isWindowActive, "Payment window should be active at the test time");
        Assert.NotEmpty(messages);
        messages.Count().ShouldBe(5);

        // Verify that at least the final payments are in the messages
        Assert.Contains(finalPayment.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(drawAdvance.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(factoringAdvance.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(notAffectedByPayment.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(processingInstantFinalPayment.PaymentRequestCommands.Last().Id, messageCommandIds);
        Assert.DoesNotContain(factoringFinal.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.DoesNotContain(finalPaymentV2.PaymentRequestCommands.First().Id, messageCommandIds);
    }

    [Fact]
    public async Task Process_FirstPaymentWindow2_ExecuteOnlyNonPrefundedPayments()
    {
        // Arrange
        // Set current time to Tuesday 9:35 AM UTC (within the prefunded payment window: 9:33-10:03)
        var currentTime = DateTime.Today.AddDays(1) + new TimeSpan(9, 35, 0);
        _dateProviderMock.CurrentDateTime.Returns(currentTime);
        _dateProviderMock.CurrentDate.Returns(DateOnly.FromDateTime(currentTime));

        // Set up payment window configuration for this test
        await SetupPaymentWindowConfigForTest(currentTime);

        // Payment that not affected by payment window
        var notAffectedByPayment = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.InvoicePayment));

        var drawAdvance = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.DrawDisbursement), paymentMethod: PaymentMethod.SameDayAch);
        var finalPayment = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FinalPayment), paymentMethod: PaymentMethod.SameDayAch);
        var finalPaymentV2 = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FinalPaymentV2), paymentMethod: PaymentMethod.SameDayAch);
        var factoringAdvance = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FactoringDisbursement), paymentMethod: PaymentMethod.SameDayAch);
        var factoringFinal = await CreateAndAssertPayment(
            PaymentRequestData.CreateValidPaymentRequest(DomainConstants.FactoringFinalPayment), paymentMethod: PaymentMethod.SameDayAch);

        // Act
        await ExecutePaymentScheduledJob();

        // Assert
        var messages = _fakePaymentFlowServiceMessageSender.GetAll();

        // Verify that final payments are prioritized (should appear first in the message queue)
        var messageCommandIds = messages.Select(x => x.ExecutableCommandId).ToList();

        // Get payment window service to verify window is active
        var paymentWindowService = _serviceProvider.GetRequiredService<IPaymentWindowService>();
        var isWindowActive = await paymentWindowService.IsPaymentWindowActive(CancellationToken.None);

        // Assert that window is active and payments were processed
        Assert.True(isWindowActive, "Payment window should be active at the test time");
        Assert.NotEmpty(messages);
        messages.Count().ShouldBe(3);

        // Verify that at least the final payments are in the messages
        Assert.DoesNotContain(finalPayment.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.DoesNotContain(drawAdvance.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.DoesNotContain(factoringAdvance.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(notAffectedByPayment.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(factoringFinal.PaymentRequestCommands.First().Id, messageCommandIds);
        Assert.Contains(finalPaymentV2.PaymentRequestCommands.First().Id, messageCommandIds);
    }

    private async Task SetupPaymentWindowConfigForTest(DateTime currentTime)
    {
        var activeDays = new List<DayOfWeek>
        {
            DayOfWeek.Monday,
            DayOfWeek.Tuesday,
            DayOfWeek.Wednesday,
            DayOfWeek.Thursday,
            DayOfWeek.Friday,
            DayOfWeek.Sunday,
            DayOfWeek.Saturday
        };

        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = true,
            AffectedPaymentTypes = new List<PaymentRequestType>
            {
                PaymentRequestType.FactoringDisbursement,
                PaymentRequestType.FactoringFinalPayment,
                PaymentRequestType.DrawDisbursement,
                PaymentRequestType.FinalPayment,
                PaymentRequestType.FinalPaymentV2,
            },
            DefaultConfig = new DefaultPaymentConfig
            {
                AllowedPaymentMethods = new List<PaymentMethod>
                {
                    PaymentMethod.Wire,
                    PaymentMethod.Instant
                },
                PaymentMethodPriorities = new Dictionary<PaymentMethod, int>
                {
                    { PaymentMethod.Ach, 5 },
                    { PaymentMethod.SameDayAch, 6 },
                    { PaymentMethod.Wire, 3 },
                    { PaymentMethod.Instant, 3 },
                    { PaymentMethod.Card, 7 }
                }

            },
            PaymentWindows = new List<PaymentWindow>
            {
                new PaymentWindow
                {
                    Id = "priority-ach",
                    Name = "Same Day ACH Window",
                    IsEnabled = true,
                    StartTime = new TimeSpan(9, 23, 0),
                    DurationMinutes = 10,
                    Priority = 1,
                    AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.SameDayAch },
                    AllowedPaymentTypes = new List<PaymentRequestType>
                    {
                        PaymentRequestType.FactoringDisbursement,
                        PaymentRequestType.DrawDisbursement,
                        PaymentRequestType.FinalPayment,
                    },
                    ActiveDays = activeDays,
                },
                new PaymentWindow
                {
                    Id = "priority-ach-prefunded",
                    Name = "Same Day ACH Window",
                    IsEnabled = true,
                    StartTime = new TimeSpan(9, 33, 0),
                    DurationMinutes = 30,
                    Priority = 2,
                    AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.SameDayAch },
                    AllowedPaymentTypes = new List<PaymentRequestType>
                    {
                        PaymentRequestType.FactoringFinalPayment,
                        PaymentRequestType.FinalPaymentV2,
                    },
                    ActiveDays = activeDays,
                },
                new PaymentWindow
                {
                    Id = "standard-ach",
                    Name = "Standard ACH Window",
                    IsEnabled = true,
                    StartTime = new TimeSpan(15, 23, 0), // 3:00 PM
                    DurationMinutes = 10,
                    Priority = 1,
                    AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Ach },
                    AllowedPaymentTypes = new List<PaymentRequestType>
                    {
                        PaymentRequestType.FactoringDisbursement,
                        PaymentRequestType.DrawDisbursement,
                        PaymentRequestType.FinalPayment,
                    },
                    ActiveDays = activeDays,
                },
                new PaymentWindow
                {
                    Id = "standard-ach-prefunded",
                    Name = "Standard ACH Window",
                    IsEnabled = true,
                    StartTime = new TimeSpan(15, 33, 0),
                    DurationMinutes = 30,
                    Priority = 2,
                    AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Ach },
                    AllowedPaymentTypes = new List<PaymentRequestType>
                    {
                        PaymentRequestType.FactoringFinalPayment,
                        PaymentRequestType.FinalPaymentV2,
                    },
                    ActiveDays = activeDays,
                }
            }
        };

        // Update the config in database and refresh cache
        var paymentWindowService = _serviceProvider.GetRequiredService<IPaymentWindowService>();
        await paymentWindowService.UpdatePaymentWindowConfig(config, "IntegrationTest", CancellationToken.None);
    }
}
