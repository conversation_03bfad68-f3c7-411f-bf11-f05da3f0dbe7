using BlueTape.PaymentService.API.ViewModels;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

/// <summary>
/// Service for managing disbursement queues and custom ordering
/// </summary>
public interface IDisbursementQueueService
{
    /// <summary>
    /// Gets disbursement queues with payment requests grouped by payment method
    /// </summary>
    /// <param name="provider">Payment provider (aion, cbw)</param>
    /// <param name="subscriptionCode">Subscription code</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>List of disbursement queues</returns>
    Task<List<DisbursementQueueViewModel>> GetDisbursementQueues(string provider, PaymentSubscriptionType subscriptionCode, CancellationToken ct);

    /// <summary>
    /// Updates the sequence order of payment requests
    /// </summary>
    /// <param name="paymentRequestIds">Payment request IDs in desired order</param>
    /// <param name="updatedBy">User making the change</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Update result</returns>
    Task<UpdateSequenceOrderResponseViewModel> UpdateSequenceOrder(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct);

    /// <summary>
    /// Unapproves a payment request (cancels approval)
    /// </summary>
    /// <param name="paymentRequestId">Payment request ID to unapprove</param>
    /// <param name="updatedBy">User making the change</param>
    /// <param name="ct">Cancellation token</param>
    Task UnapprovePaymentRequest(Guid paymentRequestId, string updatedBy, CancellationToken ct);
}
