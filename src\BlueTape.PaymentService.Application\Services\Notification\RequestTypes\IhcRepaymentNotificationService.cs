using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.Notification.RequestTypes;

/// <summary>
/// Notification service for IHC Repayment requests
/// Follows the same pattern as DrawRepaymentNotificationService since IHC repayment is similar to draw repayment
/// </summary>
public class IhcRepaymentNotificationService(
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    IPaymentTransactionRepository paymentTransactionRepository,
    ITransactionNotificationService transactionNotificationService,
    ILoggerFactory loggerFactory) : BaseNotificationService(paymentTransactionHistoryRepository, loggerFactory)
{
    private readonly ILogger _logger = loggerFactory.CreateLogger<IhcRepaymentNotificationService>();

    public override async Task ProcessTransactionUpdate(Guid transactionId, Guid transactionHistoryId, CancellationToken ct)
    {
        var transaction = await paymentTransactionRepository.GetByIdFull(transactionId, ct);
        if (transaction is null) throw new TransactionDoesNotExistException(transactionId);

        var targetHistoryItem = transaction.TransactionHistories.First(x => x.Id == transactionHistoryId);

        _logger.LogInformation("Processing IHC repayment transaction history update for transaction {TransactionId} with status {Status}",
            transaction.Id, targetHistoryItem.NewStatus);

        switch (targetHistoryItem.NewStatus)
        {
            case TransactionStatus.Error:
                break;
            case TransactionStatus.Failed:
            case TransactionStatus.Recalled:
                await transactionNotificationService.HandleIhcRepaymentFailed(transaction, transactionHistoryId, ct);
                await transactionNotificationService.HandleIhcRepaymentFailedToOpsTeam(transaction, transactionHistoryId, ct);
                break;
            case TransactionStatus.Processing:
                await transactionNotificationService.HandleIhcRepaymentProcessing(transaction, transactionHistoryId, ct);
                break;
            case TransactionStatus.Placed:
            case TransactionStatus.Processed:
            case TransactionStatus.Canceled:
            case TransactionStatus.Scheduled:
            case TransactionStatus.Aborted:
            case TransactionStatus.Hold:
                break;
            case TransactionStatus.Cleared:
                if (transaction.PaymentRequest is { Status: PaymentRequestStatus.Settled })
                {
                    await transactionNotificationService.HandleAdvancePaymentCleared(transaction, transactionHistoryId, ct);
                }
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    public override Task ProcessPaymentRequestCreated(Guid paymentRequestId, CancellationToken ct)
    {
        // IHC repayment requests don't send creation notifications (LMS handles its own notifications)
        return Task.CompletedTask;
    }
}
