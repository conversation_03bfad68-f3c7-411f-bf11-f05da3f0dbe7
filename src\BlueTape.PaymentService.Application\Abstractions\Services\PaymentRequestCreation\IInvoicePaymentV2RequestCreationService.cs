﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentV2;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IInvoicePaymentV2RequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(InvoicePaymentV2RequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
