﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IInvoicePaymentRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(InvoicePaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
