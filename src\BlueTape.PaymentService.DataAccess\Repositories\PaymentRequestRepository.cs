﻿using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Contexts;
using BlueTape.PaymentService.DataAccess.Helpers;
using BlueTape.PaymentService.DataAccess.Repositories.Base;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Base;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace BlueTape.PaymentService.DataAccess.Repositories;

public class PaymentRequestRepository(DatabaseContext context)
    : GenericRepository<PaymentRequestEntity>(context), IPaymentRequestRepository
{
    public override Task<PaymentRequestEntity?> GetById(Guid id, CancellationToken ct, bool hasNoTracking = false)
    {
        var query = hasNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        return query
            .Include(x => x.PaymentRequestPayables)
            .Include(x => x.PaymentRequestFees)
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestCommands)
            .Include(x => x.PaymentRequestDetails)
            .FirstOrDefaultAsync(x => x.Id == id, ct);
    }

    public Task<List<PaymentRequestEntity>> GetByIds(IEnumerable<Guid> ids, CancellationToken ct,
        bool hasNoTracking = false)
    {
        var query = hasNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        return query
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestCommands)
            .Where(x => ids.Contains(x.Id))
            .ToListAsync(ct);
    }

    public async Task<IEnumerable<PaymentRequestEntity>> GetByPayableId(string payableId, CancellationToken ct,
        bool hasNoTracking = false)
    {
        var query = hasNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        return await query
            .Include(x => x.PaymentRequestPayables)
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestFees)
            .Where(x => x.PaymentRequestPayables.Any(p => p.PayableId == payableId))
            .ToListAsync(ct);
    }

    public async Task<List<PaymentRequestEntity>> GetByDrawId(Guid? lmsId, CancellationToken ct)
    {
        if (lmsId == null)
        {
            return new List<PaymentRequestEntity>();
        }

        var query = DbSet.AsNoTracking();

        return await query
            .Include(x => x.PaymentRequestPayables)
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestFees)
            .Include(x => x.PaymentRequestDetails)
            .Where(x => x.PaymentRequestDetails!.DrawId == lmsId)
            .ToListAsync(ct);
    }

    public Task<PaginatedEntityResult<PaymentRequestEntity>> GetByFilter(PaymentRequestFilter filter,
        CancellationToken ct, bool hasNoTracking = false)
    {
        var query = hasNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();

        query = query.Include(x => x.PaymentRequestPayables)
            .Include(x => x.PaymentRequestFees)
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestDetails);

        if (filter.Id != null) query = query.Where(x => x.Id == filter.Id.Value);
        if (filter.FlowTemplateCodes != null && filter.FlowTemplateCodes.Count != 0)
            query = query.Where(x => filter.FlowTemplateCodes.Any(y => y.Equals(x.FlowTemplateCode)));
        if (filter.SellerId != null) query = query.Where(x => x.SellerId == filter.SellerId);
        if (filter.PayerId != null) query = query.Where(x => x.PayerId == filter.PayerId);
        if (filter.PayableId != null)
            query = query.Where(x => x.PaymentRequestPayables.Any(p => p.PayableId == filter.PayableId));

        if (filter.DateFrom != null) query = query.Where(x => x.Date >= filter.DateFrom);
        if (filter.DateTo != null) query = query.Where(x => x.Date <= filter.DateTo);
        if (filter.PaymentRequestStatuses != null && filter.PaymentRequestStatuses.Count != 0)
            query = query.Where(x => filter.PaymentRequestStatuses.Contains(x.Status));

        if (!string.IsNullOrEmpty(filter.DrawId) && Guid.TryParse(filter.DrawId, out var drawId))
        {
            query = query.Where(x => x.PaymentRequestDetails != null && x.PaymentRequestDetails.DrawId == drawId);
        }
        if (filter?.RequestType != null)
            query = query.Where(x => x.RequestType == filter.RequestType);

        query = ApplyIsConfirmed(query, filter!.IsConfirmed);
        query = ApplySearchOrCompanyFilter(query, filter.Filter, filter.CompanyIds);
        query = ApplySorting(query, filter);

        return query.BuildPaginatedEntityResult(filter.PageNumber, filter.PageSize, ct);
    }
    private static IQueryable<PaymentRequestEntity> ApplySearchOrCompanyFilter(
        IQueryable<PaymentRequestEntity> query,
        string? customFilter,
        List<string>? companyIds)
    {
        var hasCustomFilter = !string.IsNullOrEmpty(customFilter);
        var hasCompanyFilter = companyIds is { Count: > 0 };

        if (!hasCustomFilter && !hasCompanyFilter)
        {
            return query;
        }

        if (hasCustomFilter && hasCompanyFilter)
        {
            query = query.Where(x =>
                (x.PaymentRequestDetails != null && (
                    (x.PaymentRequestDetails.DrawId != null
                     && x.PaymentRequestDetails.DrawId.ToString()!.Contains(customFilter!))
                    || (x.PaymentRequestDetails.InvoiceNumber != null
                        && x.PaymentRequestDetails.InvoiceNumber.Contains(customFilter!))
                ))
                ||
                ((x.PayerId != null && companyIds!.Contains(x.PayerId)) ||
                 (x.PayeeId != null && companyIds!.Contains(x.PayeeId)) ||
                 (x.SellerId != null && companyIds!.Contains(x.SellerId)))
            );
        }
        else if (hasCustomFilter)
        {
            query = ApplyCustomFilter(query, customFilter);
        }
        else if (hasCompanyFilter)
        {
            query = ApplyCompanyFilter(query, companyIds);
        }

        return query;
    }

    private static IQueryable<PaymentRequestEntity> ApplyCustomFilter(IQueryable<PaymentRequestEntity> query,
        string? customFilter)
    {
        if (customFilter != null)
        {
            query = query.Where(x => x.PaymentRequestDetails != null && (
                    (x.PaymentRequestDetails.DrawId != null
                     && x.PaymentRequestDetails.DrawId.ToString()!.Contains(customFilter))
                    || (x.PaymentRequestDetails.InvoiceNumber != null
                        && x.PaymentRequestDetails.InvoiceNumber.Contains(customFilter))
                )
            );
        }

        return query;
    }

    private static IQueryable<PaymentRequestEntity> ApplyCompanyFilter(IQueryable<PaymentRequestEntity> query,
        List<string>? companyIds)
    {
        if (companyIds != null && companyIds.Count > 0)
        {
            query = query.Where(x =>
                (x.PayerId != null && companyIds.Contains(x.PayerId)) ||
                (x.PayeeId != null && companyIds.Contains(x.PayeeId)) ||
                (x.SellerId != null && companyIds.Contains(x.SellerId))
            );
        }

        return query;
    }

    private static IQueryable<PaymentRequestEntity> ApplyIsConfirmed(IQueryable<PaymentRequestEntity> query,
        bool? isConfirmed)
    {
        if (isConfirmed != null)
        {
            query = isConfirmed == true
                ? query.Where(x => x.ConfirmedAt != null)
                : query.Where(x => x.ConfirmedAt == null && x.ConfirmationType == ConfirmationType.Manual);
        }

        return query;
    }

    private static IOrderedQueryable<PaymentRequestEntity> ApplySorting(IQueryable<PaymentRequestEntity> query,
        PaymentRequestFilter filter)
    {
        if (filter.IsConfirmed is true)
        {
            return query.OrderByDescending(x => x.ConfirmedAt);
        }

        if (filter.SortOrder.HasValue || filter.IsConfirmed.HasValue)
        {
            var createdAtName = nameof(PaymentRequestEntity.CreatedAt);
            if (!string.IsNullOrEmpty(filter.SortBy)
                && filter.SortBy.Equals(createdAtName, StringComparison.OrdinalIgnoreCase))
            {
                return filter.SortOrder == SortOrder.asc
                    ? query.OrderBy(x => x.CreatedAt)
                    : query.OrderByDescending(x => x.CreatedAt);
            }

            return filter.SortOrder == SortOrder.asc
                ? query.OrderBy(x => x.Date)
                : query.OrderByDescending(x => x.Date);
        }

        return query.OrderByDescending(x => x.UpdatedAt);
    }

    public Task<decimal> GetTotalAmount(bool includeProcessingStatus, CancellationToken ct)
    {
        return DbSet.Where(x => x.Status == PaymentRequestStatus.Settled
                                || (includeProcessingStatus && (x.Status == PaymentRequestStatus.Processing ||
                                                                x.Status == PaymentRequestStatus.Requested ||
                                                                x.Status == PaymentRequestStatus.Processed)))
            .SumAsync(x => x.Amount, cancellationToken: ct);
    }

    public async Task<int> GetMaxSequenceNumber(PaymentMethod paymentMethod, PaymentRequestType paymentRequestType, PaymentRequestStatus status, CancellationToken ct)
    {
        var maxSequenceNumber = await DbSet
            .Where(x => x.PaymentMethod == paymentMethod &&
                       x.RequestType == paymentRequestType &&
                       x.Status == status &&
                       x.ConfirmedAt != null &&
                       x.SequenceNumber > 0)
            .MaxAsync(x => (int?)x.SequenceNumber, ct);

        return maxSequenceNumber ?? 0;
    }

    public async Task<List<PaymentRequestEntity>> GetNewlyApprovedPayments(PaymentMethod paymentMethod, PaymentRequestType paymentRequestType, List<Guid> excludePaymentIds, CancellationToken ct)
    {
        return await DbSet
            .Where(x => x.PaymentMethod == paymentMethod &&
                       x.RequestType == paymentRequestType &&
                       x.Status == PaymentRequestStatus.Requested &&
                       x.ConfirmedAt != null &&
                       !excludePaymentIds.Contains(x.Id))
            .OrderBy(x => x.ConfirmedAt)
            .ThenBy(x => x.CreatedAt)
            .ToListAsync(ct);
    }

    public async Task<int> ResetAllSequenceNumbers(string updatedBy, DateTime updatedAt, CancellationToken ct)
    {
        var affectedRows = await DbSet
            .Where(x => x.SequenceNumber > 0)
            .ExecuteUpdateAsync(x => x
                .SetProperty(p => p.SequenceNumber, 0)
                .SetProperty(p => p.UpdatedBy, updatedBy)
                .SetProperty(p => p.UpdatedAt, updatedAt), ct);

        return affectedRows;
    }
}