using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

public class SubscriptionFeePaymentRequestCreationService(
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IPaymentFlowTemplatesEngine templatesEngine,
    IInvoiceHttpClient invoiceHttpClient,
    ILogger<SubscriptionFeePaymentRequestCreationService> logger,
    IOperationSyncMessageSender operationSyncMessageSender,
    IPaymentRequestPayableService paymentRequestPayableService,
    IPaymentRequestValidator validator,
    ILoanManagementService loanManagementService)
    : BasePaymentRequestCreationService(mapper, unitOfWork, templatesEngine, invoiceHttpClient, logger, operationSyncMessageSender,
    paymentRequestPayableService, loanManagementService), ISubscriptionFeePaymentRequestCreationService
{
    public Task<PaymentRequestModel> Add(SubscriptionFeePaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct)
    {
        using (logger.BeginScope(new Dictionary<string, object>
               {
                   { "FlowTemplateCode", paymentRequestMessage.FlowTemplateCode },
                   { "CustomerCompanyId", paymentRequestMessage.PaymentRequestDetails.CustomerDetails.Id },
                   { "PaymentMethod", paymentRequestMessage.PaymentRequestDetails.PaymentMethod },
               }))
        {
            logger.LogInformation("Create subscription fee payment request started. Payload: {@PaymentRequestMessage}", paymentRequestMessage);

            var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(paymentRequestMessage);
            return Add(createPaymentRequest, createdBy, ct);
        }
    }

    protected override Task SendNotification(CreatePaymentRequestModel createPaymentRequest, PaymentRequestEntity paymentRequestEntity,
        IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct)
    {
        // No specific notification needed for subscription fee payments
        return Task.CompletedTask;
    }

    protected override Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables, LoanDto? loan,
        CancellationToken ct) =>
        validator.ValidateSubscriptionFeePaymentRequest(createPaymentRequest, ct);
}
