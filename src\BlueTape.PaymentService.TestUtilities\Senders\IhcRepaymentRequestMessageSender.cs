﻿using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.IhcRepayment;
using BlueTape.PaymentService.TestUtilities.Abstractions.Senders;
using BlueTape.ServiceBusMessaging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.TestUtilities.Senders;

public class IhcRepaymentMessageSender(
    IConfiguration configuration,
    ILogger<IhcRepaymentMessageSender> logger)
    : ServiceBusMessageSender<IhcRepaymentRequestMessage>(configuration, logger,
            InfrastructureConstants.PaymentRequestQueueName, InfrastructureConstants.PaymentRequestQueueConnection),
        IIhcRepaymentMessageSender;
