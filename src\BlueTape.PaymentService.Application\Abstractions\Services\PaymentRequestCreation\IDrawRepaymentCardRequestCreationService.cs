﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IDrawRepaymentCardRequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(DrawRepaymentCardRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
