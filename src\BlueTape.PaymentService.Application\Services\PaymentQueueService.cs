﻿using AutoMapper;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Models;

namespace BlueTape.PaymentService.Application.Services;

public class PaymentQueueService : IPaymentQueueService
{
    private readonly IPaymentJobProcessor _paymentJobProcessor;
    private readonly IMapper _mapper;

    public PaymentQueueService(IPaymentJobProcessor paymentJobProcessor, IMapper mapper)
    {
        _paymentJobProcessor = paymentJobProcessor;
        _mapper = mapper;
    }

    public async Task<IEnumerable<PaymentRequestModel>> GetDisbursementQueuesPaymentRequests(string provider, PaymentSubscriptionType subscriptionCode, CancellationToken ct)
    {
        var result = await _paymentJobProcessor.GetCommandsToExecute(ct);

        var paymentRequests = result
            .Select(c => c.PaymentRequest)
            .Where(x => x != null && x.PaymentSubscription == subscriptionCode)
            .Distinct()
            .ToList();

        return _mapper.Map<IEnumerable<PaymentRequestModel>>(paymentRequests);
    }
}
