﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IDrawDisbursementRequestCreatorService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(DrawDisbursementRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
