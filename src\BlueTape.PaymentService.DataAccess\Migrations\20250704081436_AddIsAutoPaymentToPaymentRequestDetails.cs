﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlueTape.PaymentService.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class AddIsAutoPaymentToPaymentRequestDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsAutoPayment",
                table: "PaymentRequestDetails",
                type: "boolean",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsAutoPayment",
                table: "PaymentRequestDetails");
        }
    }
}
