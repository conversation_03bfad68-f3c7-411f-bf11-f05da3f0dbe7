using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

/// <summary>
/// Service for managing sequence numbers for custom payment ordering
/// </summary>
public interface ISequenceNumberService
{
    /// <summary>
    /// Assigns a sequence number to a payment request if custom ordering is enabled
    /// </summary>
    /// <param name="paymentRequest">Payment request to assign sequence number to</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>The assigned sequence number, or 0 if custom ordering is disabled</returns>
    Task<int> AssignSequenceNumber(PaymentRequestEntity paymentRequest, CancellationToken ct);

    /// <summary>
    /// Gets the next available sequence number for a specific payment method queue
    /// </summary>
    /// <param name="paymentMethod">Payment method</param>
    /// <param name="paymentRequestType">Payment request type</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Next available sequence number</returns>
    Task<int> GetNextSequenceNumber(PaymentMethod paymentMethod, PaymentRequestType paymentRequestType, CancellationToken ct);

    /// <summary>
    /// Updates sequence numbers for a list of payment requests based on their new order
    /// </summary>
    /// <param name="paymentRequestIds">List of payment request IDs in their desired order</param>
    /// <param name="updatedBy">User making the change</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Number of payment requests updated</returns>
    Task<int> UpdateSequenceNumbers(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct);

    /// <summary>
    /// Validates that payment requests can be reordered together (same queue restrictions)
    /// </summary>
    /// <param name="paymentRequestIds">Payment request IDs to validate</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>True if all payment requests belong to the same queue</returns>
    Task<bool> ValidateReorderingRestrictions(List<Guid> paymentRequestIds, CancellationToken ct);

    /// <summary>
    /// Resets sequence numbers for all pending payment requests (useful for maintenance)
    /// </summary>
    /// <param name="updatedBy">User performing the reset</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Number of payment requests reset</returns>
    Task<int> ResetAllSequenceNumbers(string updatedBy, CancellationToken ct);
}
