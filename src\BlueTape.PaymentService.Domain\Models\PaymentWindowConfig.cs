using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Domain.Models;

public class PaymentWindow
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int DurationMinutes { get; set; }
    public TimeSpan StartTime { get; set; }
    public bool IsEnabled { get; set; }

    public List<DayOfWeek> ActiveDays { get; set; }

    public List<PaymentMethod> AllowedPaymentMethods { get; set; }

    public List<PaymentRequestType> AllowedPaymentTypes { get; set; }

    public int Priority { get; set; }
}

public class DefaultPaymentConfig
{
    public List<PaymentMethod> AllowedPaymentMethods { get; set; }

    public Dictionary<PaymentMethod, int> PaymentMethodPriorities { get; set; }
}

public class PaymentWindowConfig
{
    public bool IsPaymentWindowEnabled { get; set; }

    public List<PaymentSubscriptionType> AllowedPaymentSubscriptions { get; set; }

    public List<PaymentRequestType> AffectedPaymentTypes { get; set; }

    public DefaultPaymentConfig DefaultConfig { get; set; }

    public List<PaymentWindow> PaymentWindows { get; set; }

    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}
