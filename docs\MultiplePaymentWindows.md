# Payment Windows with Default Configuration

## Overview

The PaymentWindowService now supports a **default configuration pattern** where:
- **Default Config**: Always active configuration that handles regular payment processing
- **Payment Windows**: Specific time-based windows that temporarily override the default behavior

This approach ensures continuous payment processing with special rules during designated time periods.

## Key Concepts

### Default Configuration
- **Always Active**: Processes payments when no specific payment windows are active
- **Baseline Rules**: Defines allowed transaction types, priorities, and rate limits for normal operation
- **Fallback Behavior**: Ensures payment processing never stops completely

### Payment Windows
- **Temporary Override**: Takes precedence over default config when active
- **Specific Rules**: Can restrict or allow different transaction types during specific times
- **Priority-Based**: Multiple windows can be active simultaneously with priority handling

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Payment Processing Time                  │
├─────────────────────────────────────────────────────────────┤
│ 00:00 ─────────── 09:00 ──── 09:30 ─────── 15:00 ── 16:00 ──│
│   ↓                  ↓          ↓            ↓        ↓     │
│ Default           Window1    Default      Window2  Default  │
│ Config             Active     Config       Active   Config  │
│                                                             │
│ • All types       • ACH only  • All types  • Bulk    • All │
│ • Standard        • Priority  • Standard   • ACH     • Std │
│   priorities        rules       priorities   rules    prio │
└─────────────────────────────────────────────────────────────┘
```

## Key Features

### 1. Multiple Payment Windows
- Configure 2 or more independent payment windows
- Each window can have different schedules, durations, and allowed transaction types
- Windows can overlap or be completely separate

### 2. Window Priority System
- Each window has a priority level (lower number = higher priority)
- During overlapping windows, transactions are prioritized by window priority
- Final payments always have the highest priority regardless of window

### 3. Transaction Type Filtering
- Each window can specify which transaction types are allowed
- Common configurations:
  - ACH Push only during high-priority windows
  - All ACH types during standard windows
  - Internal transfers only during weekend windows

### 4. Flexible Scheduling
- Per-window active days configuration
- Different start times and durations for each window
- Support for weekday, weekend, or mixed schedules

## Configuration Examples

### Default Config with Specific Windows
```csharp
var config = new PaymentWindowConfig
{
    IsPaymentWindowEnabled = true,
    
    // Default configuration - always active outside payment windows
    DefaultConfig = new DefaultPaymentConfig
    {
        AllowedTransactionTypes = new List<string>
        {
            "AchPush", "AchPull", "AchInternal", "WirePush", "InstantPush"
        },
        PausedTransactionTypes = new List<string>(), // Nothing paused by default
        TransactionTypePriorities = new Dictionary<string, int>
        {
            { "AchPush", 3 },
            { "AchInternal", 4 },
            { "WirePush", 5 },
            { "InstantPush", 6 }
        },
        RateLimits = new Dictionary<string, int>
        {
            { "AchPush", 100 },    // 100 transactions per minute
            { "WirePush", 50 },
            { "InstantPush", 30 }
        }
    },
    
    // Specific payment windows that override default behavior
    PaymentWindows = new List<PaymentWindow>
    {
        new PaymentWindow
        {
            Id = "priority-ach",
            Name = "Priority ACH Processing",
            StartTime = new TimeSpan(9, 0, 0), // 9:00 AM
            DurationMinutes = 30,
            Priority = 1,
            AllowedTransactionTypes = new List<string> { "AchPush", "AchPull" } // Only ACH during priority window
        },
        new PaymentWindow
        {
            Id = "bulk-processing",
            Name = "Bulk Processing Window",
            StartTime = new TimeSpan(15, 0, 0), // 3:00 PM
            DurationMinutes = 60,
            Priority = 2,
            AllowedTransactionTypes = new List<string> { "AchPush", "AchPull", "AchInternal" }
        }
    }
};
```

### Conservative Default with Liberal Windows
```csharp
var config = new PaymentWindowConfig
{
    // Conservative default - only safe transactions
    DefaultConfig = new DefaultPaymentConfig
    {
        AllowedTransactionTypes = new List<string> { "AchInternal" }, // Only internal transfers
        PausedTransactionTypes = new List<string> { "WirePush", "InstantPush" } // Pause risky transactions
    },
    
    // Liberal windows that allow more during business hours
    PaymentWindows = new List<PaymentWindow>
    {
        new PaymentWindow
        {
            Id = "business-hours",
            StartTime = new TimeSpan(8, 0, 0),
            DurationMinutes = 480, // 8 hours (8 AM - 4 PM)
            AllowedTransactionTypes = new List<string> 
            { 
                "AchPush", "AchPull", "AchInternal", "WirePush" 
            }
        },
        new PaymentWindow
        {
            Id = "instant-window",
            StartTime = new TimeSpan(10, 0, 0),
            DurationMinutes = 120,
            AllowedTransactionTypes = new List<string> 
            { 
                "InstantPush", "AchPush", "AchPull", "AchInternal" 
            }
        }
    }
};
```

## New API Methods

### Processing Mode and Status
```csharp
// Get current processing mode description
string mode = await paymentWindowService.GetCurrentProcessingMode(ct);
// Returns: "Default Processing" or "Window Processing: Priority ACH Window"

// Get currently allowed transaction types
List<string> allowedTypes = await paymentWindowService.GetCurrentAllowedTransactionTypes(ct);
// Returns: ["AchPush", "AchPull"] during window or ["AchPush", "WirePush", "InstantPush"] during default

// Check if specific transaction should process now
bool shouldProcess = await paymentWindowService.ShouldProcessTransactionType(
    PaymentTransactionType.AchPush, ct);
```

### Window Information
```csharp
// Check if any window is active
bool isActive = await paymentWindowService.IsPaymentWindowActive(ct);

// Get all active windows
List<PaymentWindow> activeWindows = await paymentWindowService.GetActivePaymentWindows(ct);

// Get highest priority active window
PaymentWindow? primaryWindow = await paymentWindowService.GetActivePaymentWindow(ct);

// Get next window start time
DateTime? nextStart = await paymentWindowService.GetNextPaymentWindowStartTime(ct);
```

## Processing Logic Flow

### Decision Tree
```
Is PaymentWindowEnabled?
├─ No → Use DefaultConfig
│  ├─ Check AllowedTransactionTypes
│  ├─ Exclude PausedTransactionTypes  
│  └─ Apply TransactionTypePriorities
│
└─ Yes → Check for Active Windows
   ├─ Active Windows Found
   │  ├─ Use Window AllowedTransactionTypes
   │  ├─ Apply Window Priority + Transaction Priority
   │  └─ Process with Window Rules
   │
   └─ No Active Windows → Use DefaultConfig
      ├─ Check AllowedTransactionTypes
      ├─ Exclude PausedTransactionTypes
      └─ Apply TransactionTypePriorities
```

### Priority Calculation
- **Final Payments**: Always priority 0 (highest)
- **During Windows**: `(Window Priority × 100) + Transaction Type Priority`
- **Default Processing**: `1000 + Default Transaction Priority`

### Rate Limiting
Default configuration includes rate limits per transaction type:
```csharp
RateLimits = new Dictionary<string, int>
{
    { "AchPush", 100 },      // 100 transactions per minute
    { "AchInternal", 200 },  // 200 transactions per minute  
    { "WirePush", 50 },      // 50 transactions per minute
    { "InstantPush", 30 }    // 30 transactions per minute
}
```

## Common Use Cases

### Use Case 1: Standard Business Operations
- **Default**: Allow all standard transaction types with normal rate limits
- **9 AM Window**: Priority ACH processing with higher throughput
- **3 PM Window**: Bulk ACH processing for end-of-day settlements

### Use Case 2: Risk Management
- **Default**: Conservative - only internal transfers allowed
- **Business Hours**: Supervised processing - allow ACH and wires
- **Special Window**: Instant payments only during monitored periods

### Use Case 3: Performance Optimization  
- **Default**: Standard processing with moderate rate limits
- **Night Window**: High-throughput bulk processing with relaxed limits
- **Peak Hours**: Restricted processing to maintain system performance

### Use Case 4: Regulatory Compliance
- **Default**: Compliant transaction types only
- **Compliance Window**: Extended transaction types during supervised hours
- **Audit Period**: Restricted processing during regulatory reviews

## Backward Compatibility

The service maintains backward compatibility with existing configurations:
- Legacy properties (`PaymentWindowDurationMinutes`, `PaymentWindowStartTime`, `ActiveDays`) map to the first payment window
- Existing configurations will automatically work with the new system
- Legacy properties are marked as `[Obsolete]` to encourage migration

## Migration Guide

### From Single Window to Multiple Windows

1. **Update Configuration Structure**:
   ```csharp
   // Old way
   config.PaymentWindowStartTime = new TimeSpan(9, 0, 0);
   config.PaymentWindowDurationMinutes = 30;
   
   // New way
   config.PaymentWindows = new List<PaymentWindow>
   {
       new PaymentWindow
       {
           Id = "primary",
           StartTime = new TimeSpan(9, 0, 0),
           DurationMinutes = 30
       }
   };
   ```

2. **Add Second Window**:
   ```csharp
   config.PaymentWindows.Add(new PaymentWindow
   {
       Id = "secondary",
       Name = "Afternoon Processing",
       StartTime = new TimeSpan(15, 0, 0),
       DurationMinutes = 45,
       Priority = 2
   });
   ```

3. **Update Business Logic**:
   - Replace `IsPaymentWindowActive()` checks with `GetActivePaymentWindows()` if you need window-specific logic
   - Use `GetActivePaymentWindow()` to get the current highest-priority window
   - Update any hardcoded time calculations to use the new window-specific methods

## Example Use Cases

### Use Case 1: Regional Processing
- **Morning Window (8:00 AM)**: East Coast ACH processing
- **Afternoon Window (2:00 PM)**: West Coast ACH processing

### Use Case 2: Transaction Type Separation
- **Priority Window (8:30 AM)**: Critical ACH Push only
- **Standard Window (2:00 PM)**: All ACH transaction types
- **Weekend Window (10:00 AM)**: Internal transfers only

### Use Case 3: Volume Management
- **Quick Window (9:00 AM, 30 min)**: High-priority transactions
- **Extended Window (2:00 PM, 90 min)**: Standard processing with larger capacity

### Use Case 4: Overlapping Windows
- **Critical Window (9:00 AM - 10:00 AM, Priority 1)**: ACH Push/Pull only
- **Standard Window (9:30 AM - 11:00 AM, Priority 2)**: All ACH types
- During overlap (9:30-10:00): Critical window takes priority for ACH Push/Pull

## Testing

The enhanced service includes comprehensive test coverage for:
- Multiple window activation scenarios
- Priority-based command sorting
- Transaction type filtering across windows
- Overlapping window handling
- Backward compatibility with legacy configurations

See `PaymentWindowServiceUsageExamples.cs` for practical implementation examples.
