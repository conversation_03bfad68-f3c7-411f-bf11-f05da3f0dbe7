﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoiceDisbursementV2;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IInvoiceDisbursementV2RequestCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(InvoiceDisbursementV2RequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
